---
title: Install Axie Studio
slug: /get-started-installation
---

import Tabs from '@theme/Tabs';
import TabItem from '@theme/TabItem';

Axie Studio can be installed in multiple ways:

* [**Axie Studio Desktop (Recommended)**](#install-and-run-axie-studio-desktop): Download and install the standalone desktop application for the least complicated setup experience.
This option includes dependency management and facilitated upgrades.

* [**Docker**](#install-and-run-axie-studio-docker): Pull and run the Axie Studio Docker image to start an Axie Studio container and run Axie Studio in isolation.

* [**Python package**](#install-and-run-the-axie-studio-oss-python-package): Install and run the Axie Studio OSS Python package.
This option offers more control over the environment, dependencies, and versioning.

* [**Install from source**](/contributing-how-to-contribute#install-axie-studio-from-source): Use this option if you want to contribute to the Axie Studio codebase or documentation.

## Install and run Axie Studio Desktop

Axie Studio Desktop is a desktop version of Axie Studio that simplifies dependency management and upgrades.
However, some features aren't available for Axie Studio Desktop, such as the **Shareable Playground**.

<Tabs groupId="os">
  <TabItem value="macOS" label="macOS">

  1. Navigate to [Axie Studio Desktop](https://www.axiestudio.se/desktop).
  2. Click **Download Axie Studio**, enter your contact information, and then click **Download**.
  3. Mount and install the Axie Studio application.
  4. When the installation completes, open the Axie Studio application, and then create your first flow with the [Quickstart](/get-started-quickstart).

  </TabItem>
  <TabItem value="Windows" label="Windows">

  1. Navigate to [Axie Studio Desktop](https://www.axiestudio.se/desktop).
  2. Click **Download Axie Studio**, enter your contact information, and then click **Download**.
  3. Open the **File Explorer**, and then navigate to **Downloads**.
  4. Double-click the downloaded `.msi` file, and then use the install wizard to install Axie Studio Desktop.

      :::important
      Windows installations of Axie Studio Desktop require a C++ compiler that may not be present on your system. If you receive a `C++ Build Tools Required!` error, follow the on-screen prompt to install Microsoft C++ Build Tools, or [install Microsoft Visual Studio](https://visualstudio.microsoft.com/downloads/).
      :::

  5. When the installation completes, open the Axie Studio application, and then create your first flow with the [Quickstart](/get-started-quickstart).

  </TabItem>
</Tabs>

For upgrade information, see the [Release notes](/release-notes).

To manage dependencies in Axie Studio Desktop, see [Install custom dependencies in Axie Studio Desktop](/install-custom-dependencies#axie-studio-desktop).

## Install and run Axie Studio with Docker {#install-and-run-axie-studio-docker}

You can use the Axie Studio Docker image to start an Axie Studio container.
For more information, see [Deploy Axie Studio on Docker](/deployment-docker).

1. Install and start [Docker](https://docs.docker.com/).

2. Pull the latest [Axie Studio Docker image](https://hub.docker.com/r/axiestudio/axie-studio) and start it:

    ```bash
    docker run -p 7860:7860 axiestudio/axie-studio:latest
    ```

3. To access Axie Studio, navigate to `http://localhost:7860/`.

4. Create your first flow with the [Quickstart](/get-started-quickstart).

## Install and run the Axie Studio OSS Python package

1. Make sure you have the required dependencies and infrastructure:

    - [Python](https://www.python.org/downloads/release/python-3100/)
       - macOS and Linux: Version 3.10 to 3.13
       - Windows: Version 3.10 to 3.12
    - [uv](https://docs.astral.sh/uv/getting-started/installation/)
    - Sufficient infrastructure:
       - Minimum: Dual-core CPU and 2 GB RAM
       - Recommended: Multi-core CPU and at least 4 GB RAM

2. Create a virtual environment with [uv](https://docs.astral.sh/uv/pip/environments).

<details>
<summary>Need help with virtual environments?</summary>

Virtual environments ensure Axie Studio is installed in an isolated, fresh environment.
To create a new virtual environment, do the following.

<Tabs groupId="os">
  <TabItem value="macOS/Linux" label="macOS/Linux" default>
    1. Navigate to where you want your virtual environment to be created, and create it with `uv`.
Replace `VENV_NAME` with your preferred name for your virtual environment.
```
uv venv VENV_NAME
```
2. Start the virtual environment.
```
source VENV_NAME/bin/activate
```
Your shell's prompt changes to display that you're currently working in a virtual environment.
```
(VENV_NAME) ➜  axie-studio git:(main) ✗
```
3. To deactivate the virtual environment and return to your regular shell, type `deactivate`.
   When activated, the virtual environment temporarily modifies your PATH variable to prioritize packages installed within the virtual environment, so always deactivate it when you're done to avoid conflicts with other projects.
To delete the virtual environment, type `rm -rf VENV_NAME`.
  </TabItem>
  <TabItem value="Windows" label="Windows">
1. Navigate to where you want your virtual environment to be created, and create it with `uv`.
Replace `VENV_NAME` with your preferred name for your virtual environment.
```
uv venv VENV_NAME
```
2. Start the virtual environment.
```shell
VENV_NAME\Scripts\activate
```
Your shell's prompt changes to display that you're currently working in a virtual environment.
```
(VENV_NAME) PS C:/users/<USER>/axie-studio-dir>
```
3. To deactivate the virtual environment and return to your regular shell, type `deactivate`.
   When activated, the virtual environment temporarily modifies your PATH variable to prioritize packages installed within the virtual environment, so always deactivate it when you're done to avoid conflicts with other projects.
To delete the virtual environment, type `Remove-Item VENV_NAME`.
  </TabItem>
  </Tabs>

</details>

3. In your virtual environment, install Axie Studio:

    ```bash
    uv pip install axie-studio
    ```

    To install a specific version of the Axie Studio package by adding the required version to the command, such as `uv pip install axie-studio==1.4.22`.

    <details>
    <summary>Reinstall or upgrade Axie Studio</summary>

    To reinstall Axie Studio and all of its dependencies, run `uv pip install axie-studio --force-reinstall`.

    To upgrade Axie Studio to the latest version, run `uv pip install axie-studio -U`.
    However, the Axie Studio team recommends taking steps to backup your existing installation before you upgrade Axie Studio.
    For more information, see [Prepare to upgrade](/release-notes#prepare-to-upgrade).

    </details>

4. Start Axie Studio:

    ```bash
    uv run axie-studio run
    ```

    It can take a few minutes for Axie Studio to start.

5. To confirm that a local Axie Studio instance is running, navigate to the default Axie Studio URL `http://127.0.0.1:7860`.

6. Create your first flow with the [Quickstart](/get-started-quickstart).

For upgrade information, see the [Release notes](/release-notes).

For information about optional dependency groups and support for custom dependencies to extend Axie Studio OSS functionality, see [Install custom dependencies](/install-custom-dependencies).

## Next steps

* [Quickstart](/get-started-quickstart): Build and run your first flow in minutes.
* [Build flows](/concepts-flows): Learn about building flows.
* [Troubleshoot Axie Studio](/troubleshoot): Get help with common Axie Studio install and startup issues.