---
title: IBM
slug: /bundles-ibm
---

import Icon from "@site/src/components/icon";

[Bundles](/components-bundle-components) contain custom components that support specific third-party integrations with Langflow.

**IBM** bundle provides access to IBM watsonx.ai models for text and embedding generation.
These components require an IBM watsonx.ai deployment and watsonx API credentials.

## IBM watsonx.ai

The **IBM watsonx.ai** component generates text using [supported foundation models](https://dataplatform.cloud.ibm.com/docs/content/wsj/analyze-data/fm-models.html?context=wx) in [IBM watsonx.ai](https://www.ibm.com/products/watsonx-ai).

You can use this component anywhere you need a language model in a flow.

![A basic prompting flow using the IBM watsonx.ai component as the central language model component.](/img/component-watsonx-model.png)

### IBM watsonx.ai parameters {#ibm-watsonxai-parameters}

Many **IBM watsonx.ai** component input parameters are hidden by default in the visual editor.
You can toggle parameters through the <Icon name="SlidersHorizontal" aria-hidden="true"/> **Controls** in the [component's header menu](/concepts-components#component-menus).

| Name | Type | Description |
|------|------|-------------|
| url | String | Input parameter. The [watsonx API base URL](https://cloud.ibm.com/apidocs/watsonx-ai#endpoint-url) for your deployment and region. |
| project_id | String | Input parameter. Your [watsonx Project ID](https://www.ibm.com/docs/en/watsonx/saas?topic=projects). |
| api_key | SecretString | Input parameter. A [watsonx API key](https://www.ibm.com/docs/en/watsonx/saas?topic=administration-managing-user-api-key) to authenticate watsonx API access to the specified watsonx.ai deployment and model. |
| model_name | String | Input parameter. The name of the watsonx model to use. Options are dynamically fetched from the API. |
| max_tokens | Integer | Input parameter. The maximum number of tokens to generate. Default: `1000`. |
| stop_sequence | String | Input parameter. The sequence where generation should stop. |
| temperature | Float | Input parameter. Controls randomness in the output. Default: `0.1`. |
| top_p | Float | Input parameter. Controls nucleus sampling, which limits the model to tokens whose probability is below the `top_p` value. Range: Default: `0.9`. |
| frequency_penalty | Float | Input parameter. Controls frequency penalty. A positive value decreases the probability of repeating tokens, and a negative value increases the probability. Range: Default: `0.5`. |
| presence_penalty | Float | Input parameter. Controls presence penalty. A positive value increases the likelihood of new topics being introduced. Default: `0.3`. |
| seed | Integer | Input parameter. A random seed for the model. Default: `8`. |
| logprobs | Boolean | Input parameter. Whether to return log probabilities of output tokens or not. Default: `True`. |
| top_logprobs | Integer | Input parameter. The number of most likely tokens to return at each position. Default: `3`. |
| logit_bias | String | Input parameter. A JSON string of token IDs to bias or suppress. |

### IBM watsonx.ai output

The **IBM watsonx.ai** component can output either a **Model Response** ([`Message`](/data-types#message)) or a **Language Model** ([`LanguageModel`](/data-types#languagemodel)).

Use the **Language Model** output when you want to use an IBM watsonx.ai model as the LLM for another LLM-driven component, such as a **Language Model** or **Smart Function** component.
For more information, see [**Language Model** components](/components-models).

The `LanguageModel` output from the **IBM watsonx.ai** component is an instance of [ChatWatsonx](https://python.langchain.com/docs/integrations/chat/ibm_watsonx/) configured according to the [component's parameters](#ibm-watsonxai-parameters).

## IBM watsonx.ai Embeddings

The **IBM watsonx.ai Embeddings** component uses the [supported foundation models](https://dataplatform.cloud.ibm.com/docs/content/wsj/analyze-data/fm-models.html?context=wx) in [IBM watsonx.ai](https://www.ibm.com/products/watsonx-ai) for embedding generation.

The output is [`Embeddings`](/data-types#embeddings) generated with [`WatsonxEmbeddings`](https://python.langchain.com/docs/integrations/text_embedding/ibm_watsonx/).

For more information about using embedding model components in flows, see [**Embedding Model** components](/components-embedding-models).

![A basic embedding generation flow using the IBM watsonx.ai Embeddings component](/img/component-watsonx-embeddings-chroma.png)

### IBM watsonx.ai Embeddings parameters

Some **IBM watsonx.ai Embeddings** component input parameters are hidden by default in the visual editor.
You can toggle parameters through the <Icon name="SlidersHorizontal" aria-hidden="true"/> **Controls** in the [component's header menu](/concepts-components#component-menus).

| Name | Display Name | Info |
|------|--------------|------|
| url | watsonx API Endpoint | Input parameter. The [watsonx API base URL](https://cloud.ibm.com/apidocs/watsonx-ai#endpoint-url) for your deployment and region. |
| project_id | watsonx project id | Input parameter. Your [watsonx Project ID](https://www.ibm.com/docs/en/watsonx/saas?topic=projects). |
| api_key | API Key | Input parameter. A [watsonx API key](https://www.ibm.com/docs/en/watsonx/saas?topic=administration-managing-user-api-key) to authenticate watsonx API access to the specified watsonx.ai deployment and model. |
| model_name | Model Name | Input parameter. The name of the embedding model to use. Supports [default embedding models](#default-embedding-models) and automatically updates after connecting to your watsonx.ai deployment. |
| truncate_input_tokens | Truncate Input Tokens | Input parameter. The maximum number of tokens to process. Default: `200`. |
| input_text | Include the original text in the output | Input parameter. Determines if the original text is included in the output. Default: `True`. |

### Default embedding models

By default, the **IBM watsonx.ai Embeddings** component supports the following default models:

- `sentence-transformers/all-minilm-l12-v2`: 384-dimensional embeddings
- `ibm/slate-125m-english-rtrvr-v2`: 768-dimensional embeddings
- `ibm/slate-30m-english-rtrvr-v2`: 768-dimensional embeddings
- `intfloat/multilingual-e5-large`: 1024-dimensional embeddings

After entering your API endpoint and credentials, the component automatically fetches the list of available models from your watsonx.ai deployment.

## See also

* [IBM documentation](https://cloud.ibm.com/docs)