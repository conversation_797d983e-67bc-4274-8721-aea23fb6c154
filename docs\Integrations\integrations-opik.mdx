---
title: Opik
slug: /integrations-opik
---

import Tabs from '@theme/Tabs';
import TabItem from '@theme/TabItem';

[Opik](https://www.comet.com/site/products/opik/) is an open-source platform designed for evaluating, testing, and monitoring large language model (LLM) applications. Developed by Comet, it aims to facilitate more intuitive collaboration, testing, and monitoring of LLM-based applications.

You can configure Langflow to collect [tracing](https://www.comet.com/docs/opik/tracing/log_traces) data about your flow executions and automatically send the data to Opik.

## Prerequisites

- An [Open-Source Opik server or an Opik Cloud account](https://www.comet.com/docs/opik/faq#what-is-the-difference-between-opik-cloud-and-the-open-source-opik-platform-)
- A [running Langflow server](/get-started-installation) with a [flow](/concepts-flows) that you want to trace

:::tip
If you need a flow to test the Opik integration, see the [Langflow quickstart](/get-started-quickstart).
:::

## Integrate Opik with Langflow

1. If you use Opik Cloud, get an [Opik API key](https://www.comet.com/docs/opik/faq#where-can-i-find-my-opik-api-key-).

    An API key isn't required with an Open-Source Opik server.

2. Call the `opik configure` CLI to save your Opik configuration in the same environment where you run Langflow:

    ```bash
    opik configure
    ```

    For self-hosted Opik, you can also use the following Opik CLI command:

    ```bash
    opik configure --use_local
    ```

    For more information, see the [Opik SDK configuration documentation](https://www.comet.com/docs/opik/tracing/sdk_configuration).

3. Start Langflow in the same terminal or environment where you set the environment variables:

    ```bash
    uv run langflow run
    ```

4. In Langflow, run a flow to produce activity for Opik to trace.

5. Navigate to your Opik project dashboard and view the collected tracing data.

## Disable the Opik integration

To disable the Opik integration, remove the environment variables you set with `opik configure`, and then restart Langflow.