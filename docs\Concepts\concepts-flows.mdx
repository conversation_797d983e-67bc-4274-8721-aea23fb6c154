---
title: Build flows
slug: /concepts-flows
---

import Icon from "@site/src/components/icon";

A _flow_ is a functional representation of an application workflow.
Flows receive input, process it, and produce output.

Flows consist of _components_ that represent individual steps in your application's workflow.

![Basic prompting flow within the Workspace](/img/workspace-basic-prompting.png)

Langflow flows are fully serializable and can be saved and loaded from the file system where Langflow is installed.

:::tip
To try building and running a flow in a few minutes, see the [Langflow quickstart](/get-started-quickstart).
:::

## Create a flow

There are four ways to create a flow in the Langflow UI:

* **Create a blank flow**: From the [**Projects** page](#projects), select a project, and then click **New Flow**.
* **Create a flow from a template**: From the [**Projects** page](#projects), select a project, and then click **New Flow**.
* **Duplicate an existing flow**: From the [**Projects** page](#projects), locate the flow you want to copy, click <Icon name="Ellipsis" aria-hidden="true" /> **More**, and then select **Duplicate**.
* **Import a flow**: See [Import and export flows](/concepts-flows-import).

You can also create a flow with the [Langflow API](/api-flows), but the Langflow team recommends using the visual editor until you are familiar with flow creation.

### Add components

Flows consist of [components](/concepts-components), which are nodes that you configure and connect in the Langflow [visual editor](/concepts-overview).
Each component performs a specific task, like serving an AI model or connecting a data source.

Drag and drop components from the **Components** menu to add them to your flow.
Then, configure the component settings and connect the components together.

![Chat input and output connected to Language model component](/img/connect-component.png)

Each component has configuration settings and options. Some of these are common to all components, and some are unique to specific components.

To form a cohesive flow, you connect components by _edges_ or _ports_, which have a specific data type they receive or send.
For example, message ports send text strings between components.

For more information about component configuration, including port types and underlying component code, see [Components overview](/concepts-components).

### Run a flow

After you build a prototype flow, you can test it in the [**Playground**](/concepts-playground).
When you're ready to use Langflow for application development, learn how to [trigger flows with the Langflow API](/concepts-publish), explore more advanced configuration options like [custom dependencies](/install-custom-dependencies), and, eventually, [containerize your Langflow application](/develop-application).

When you're ready to go to production or deploy a Langflow MCP server for access over the public internet, see [Langflow deployment overview](/deployment-overview).

#### Flow graphs

When a flow runs, Langflow builds a Directed Acyclic Graph (DAG) object from the nodes (components) and edges (connections), and the nodes are sorted to determine the order of execution.

The graph build calls each component's `def_build` function to validate and prepare the nodes.
This graph is then processed in dependency order.
Each node is built and executed sequentially, with results from each built node being passed to nodes that are dependent on that node's results.

## Manage flows in projects {#projects}

The **Projects** page is where you arrive when you launch Langflow.
It is where you view and manage flows on a high level.

Langflow projects are like folders that you can use to organize related flows.
The default project is **Starter Project**, and your flows are stored here unless you create another project.
To create a project, click <Icon name="Plus" aria-hidden="true"/> **Create new project**.

![Projects page with multiple flows in a project](/img/my-projects.png)

From the **Projects** page, you can manage flows within each of your projects:
* **View flows in a project**: Select the project name in the **Projects** list.
* **Create flows**: See [Create a flow](#create-a-flow).
* **Edit a flow's name and description**: Locate the flow you want to edit, click <Icon name="Ellipsis" aria-hidden="true" /> **More**, and then select **Edit details**.
* **Delete a flow**: Locate the flow you want to delete, click <Icon name="Ellipsis" aria-hidden="true" /> **More**, and then select **Delete**.
* **Serve flows as MCP tools**: See [Use Langflow as an MCP server](/mcp-server).

## Flow storage and logs

By default, flows and [flow logs](/logging) are stored on local disk at the following default locations:

- **macOS Desktop**: `/Users/<USER>/.langflow/cache`
- **Windows Desktop**: `C:\Users\<USER>\AppData\Roaming\com.Langflow\cache`
- **OSS macOS/Windows/Linux/WSL (uv pip install)**: `<path_to_venv>/lib/python3.12/site-packages/langflow/cache`
- **OSS macOS/Windows/Linux/WSL (git clone)**: `<path_to_clone>/src/backend/base/langflow/cache`

The flow storage location can be customized with the [`LANGFLOW_CONFIG_DIR`](/environment-variables#LANGFLOW_CONFIG_DIR) environment variable, and the flow log storage location can be customized with the [`LANGFLOW_LOG_FILE`](/environment-variables#LANGFLOW_LOG_FILE) environment variable.

## See also

* [Share and embed flows](/concepts-publish)
* [Import and export flows](/concepts-flows-import)
* [Langflow environment variables](/environment-variables)