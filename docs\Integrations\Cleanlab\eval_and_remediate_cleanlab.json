{"data": {"edges": [{"animated": false, "className": "", "data": {"sourceHandle": {"dataType": "ChatInput", "id": "ChatInput-wk8QV", "name": "message", "output_types": ["Message"]}, "targetHandle": {"fieldName": "input_value", "id": "OpenAIModel-1P8nP", "inputTypes": ["Message"], "type": "str"}}, "id": "reactflow__edge-ChatInput-wk8QV{œdataTypeœ:œChatInputœ,œidœ:œChatInput-wk8QVœ,œnameœ:œmessageœ,œoutput_typesœ:[œMessageœ]}-OpenAIModel-1P8nP{œfieldNameœ:œinput_valueœ,œidœ:œOpenAIModel-1P8nPœ,œinputTypesœ:[œMessageœ],œtypeœ:œstrœ}", "selected": false, "source": "ChatInput-wk8QV", "sourceHandle": "{œdataTypeœ:œChatInputœ,œidœ:œChatInput-wk8QVœ,œnameœ:œmessageœ,œoutput_typesœ:[œMessageœ]}", "target": "OpenAIModel-1P8nP", "targetHandle": "{œfieldNameœ:œinput_valueœ,œidœ:œOpenAIModel-1P8nPœ,œinputTypesœ:[œMessageœ],œtypeœ:œstrœ}"}, {"animated": false, "className": "", "data": {"sourceHandle": {"dataType": "Prompt", "id": "Prompt-MabiH", "name": "prompt", "output_types": ["Message"]}, "targetHandle": {"fieldName": "system_message", "id": "OpenAIModel-1P8nP", "inputTypes": ["Message"], "type": "str"}}, "id": "reactflow__edge-Prompt-<PERSON><PERSON><PERSON>{œdataTypeœ:œPromptœ,œidœ:œPrompt-MabiHœ,œnameœ:œpromptœ,œoutput_typesœ:[œMessageœ]}-OpenAIModel-1P8nP{œfieldNameœ:œsystem_messageœ,œidœ:œOpenAIModel-1P8nPœ,œinputTypesœ:[œMessageœ],œtypeœ:œstrœ}", "selected": false, "source": "Prompt-MabiH", "sourceHandle": "{œdataTypeœ:œPromptœ,œidœ:œPrompt-MabiHœ,œnameœ:œpromptœ,œoutput_typesœ:[œMessageœ]}", "target": "OpenAIModel-1P8nP", "targetHandle": "{œfieldNameœ:œsystem_messageœ,œidœ:œOpenAIModel-1P8nPœ,œinputTypesœ:[œMessageœ],œtypeœ:œstrœ}"}, {"animated": false, "className": "", "data": {"sourceHandle": {"dataType": "OpenAIModel", "id": "OpenAIModel-1P8nP", "name": "text_output", "output_types": ["Message"]}, "targetHandle": {"fieldName": "response", "id": "CleanlabEvaluator-Pfyl2", "inputTypes": ["Message"], "type": "str"}}, "id": "reactflow__edge-OpenAIModel-1P8nP{œdataTypeœ:œOpenAIModelœ,œidœ:œOpenAIModel-1P8nPœ,œnameœ:œtext_outputœ,œoutput_typesœ:[œMessageœ]}-CleanlabEvaluator-Pfyl2{œfieldNameœ:œresponseœ,œidœ:œCleanlabEvaluator-Pfyl2œ,œinputTypesœ:[œMessageœ],œtypeœ:œstrœ}", "selected": false, "source": "OpenAIModel-1P8nP", "sourceHandle": "{œdataTypeœ:œOpenAIModelœ,œidœ:œOpenAIModel-1P8nPœ,œnameœ:œtext_outputœ,œoutput_typesœ:[œMessageœ]}", "target": "CleanlabEvaluator-Pfyl2", "targetHandle": "{œfieldNameœ:œresponseœ,œidœ:œCleanlabEvaluator-Pfyl2œ,œinputTypesœ:[œMessageœ],œtypeœ:œstrœ}"}, {"animated": false, "className": "", "data": {"sourceHandle": {"dataType": "Prompt", "id": "Prompt-MabiH", "name": "prompt", "output_types": ["Message"]}, "targetHandle": {"fieldName": "system_prompt", "id": "CleanlabEvaluator-Pfyl2", "inputTypes": ["Message"], "type": "str"}}, "id": "reactflow__edge-Prompt-<PERSON><PERSON><PERSON>{œdataTypeœ:œPromptœ,œidœ:œPrompt-MabiHœ,œnameœ:œpromptœ,œoutput_typesœ:[œMessageœ]}-CleanlabEvaluator-Pfyl2{œfieldNameœ:œsystem_promptœ,œidœ:œCleanlabEvaluator-Pfyl2œ,œinputTypesœ:[œMessageœ],œtypeœ:œstrœ}", "selected": false, "source": "Prompt-MabiH", "sourceHandle": "{œdataTypeœ:œPromptœ,œidœ:œPrompt-MabiHœ,œnameœ:œpromptœ,œoutput_typesœ:[œMessageœ]}", "target": "CleanlabEvaluator-Pfyl2", "targetHandle": "{œfieldNameœ:œsystem_promptœ,œidœ:œCleanlabEvaluator-Pfyl2œ,œinputTypesœ:[œMessageœ],œtypeœ:œstrœ}"}, {"animated": false, "className": "", "data": {"sourceHandle": {"dataType": "ChatInput", "id": "ChatInput-wk8QV", "name": "message", "output_types": ["Message"]}, "targetHandle": {"fieldName": "prompt", "id": "CleanlabEvaluator-Pfyl2", "inputTypes": ["Message"], "type": "str"}}, "id": "reactflow__edge-ChatInput-wk8QV{œdataTypeœ:œChatInputœ,œidœ:œChatInput-wk8QVœ,œnameœ:œmessageœ,œoutput_typesœ:[œMessageœ]}-CleanlabEvaluator-Pfyl2{œfieldNameœ:œpromptœ,œidœ:œCleanlabEvaluator-Pfyl2œ,œinputTypesœ:[œMessageœ],œtypeœ:œstrœ}", "selected": false, "source": "ChatInput-wk8QV", "sourceHandle": "{œdataTypeœ:œChatInputœ,œidœ:œChatInput-wk8QVœ,œnameœ:œmessageœ,œoutput_typesœ:[œMessageœ]}", "target": "CleanlabEvaluator-Pfyl2", "targetHandle": "{œfieldNameœ:œpromptœ,œidœ:œCleanlabEvaluator-Pfyl2œ,œinputTypesœ:[œMessageœ],œtypeœ:œstrœ}"}, {"animated": false, "className": "", "data": {"sourceHandle": {"dataType": "CleanlabEvaluator", "id": "CleanlabEvaluator-Pfyl2", "name": "score", "output_types": ["number"]}, "targetHandle": {"fieldName": "score", "id": "CleanlabRemediator-YB1Ss", "inputTypes": ["number"], "type": "other"}}, "id": "reactflow__edge-CleanlabEvaluator-Pfyl2{œdataTypeœ:œCleanlabEvaluatorœ,œidœ:œCleanlabEvaluator-Pfyl2œ,œnameœ:œscoreœ,œoutput_typesœ:[œnumberœ]}-CleanlabRemediator-YB1Ss{œfieldNameœ:œscoreœ,œidœ:œCleanlabRemediator-YB1Ssœ,œinputTypesœ:[œnumberœ],œtypeœ:œotherœ}", "selected": false, "source": "CleanlabEvaluator-Pfyl2", "sourceHandle": "{œdataTypeœ:œCleanlabEvaluatorœ,œidœ:œCleanlabEvaluator-Pfyl2œ,œnameœ:œscoreœ,œoutput_typesœ:[œnumberœ]}", "target": "CleanlabRemediator-YB1Ss", "targetHandle": "{œfieldNameœ:œscoreœ,œidœ:œCleanlabRemediator-YB1Ssœ,œinputTypesœ:[œnumberœ],œtypeœ:œotherœ}"}, {"animated": false, "className": "", "data": {"sourceHandle": {"dataType": "CleanlabEvaluator", "id": "CleanlabEvaluator-Pfyl2", "name": "explanation", "output_types": ["Message"]}, "targetHandle": {"fieldName": "explanation", "id": "CleanlabRemediator-YB1Ss", "inputTypes": ["Message"], "type": "str"}}, "id": "reactflow__edge-CleanlabEvaluator-Pfyl2{œdataTypeœ:œCleanlabEvaluatorœ,œidœ:œCleanlabEvaluator-Pfyl2œ,œnameœ:œexplanationœ,œoutput_typesœ:[œMessageœ]}-CleanlabRemediator-YB1Ss{œfieldNameœ:œexplanationœ,œidœ:œCleanlabRemediator-YB1Ssœ,œinputTypesœ:[œMessageœ],œtypeœ:œstrœ}", "selected": false, "source": "CleanlabEvaluator-Pfyl2", "sourceHandle": "{œdataTypeœ:œCleanlabEvaluatorœ,œidœ:œCleanlabEvaluator-Pfyl2œ,œnameœ:œexplanationœ,œoutput_typesœ:[œMessageœ]}", "target": "CleanlabRemediator-YB1Ss", "targetHandle": "{œfieldNameœ:œexplanationœ,œidœ:œCleanlabRemediator-YB1Ssœ,œinputTypesœ:[œMessageœ],œtypeœ:œstrœ}"}, {"animated": false, "className": "", "data": {"sourceHandle": {"dataType": "CleanlabEvaluator", "id": "CleanlabEvaluator-Pfyl2", "name": "response_passthrough", "output_types": ["Message"]}, "targetHandle": {"fieldName": "response", "id": "CleanlabRemediator-YB1Ss", "inputTypes": ["Message"], "type": "str"}}, "id": "reactflow__edge-CleanlabEvaluator-Pfyl2{œdataTypeœ:œCleanlabEvaluatorœ,œidœ:œCleanlabEvaluator-Pfyl2œ,œnameœ:œresponse_passthroughœ,œoutput_typesœ:[œMessageœ]}-CleanlabRemediator-YB1Ss{œfieldNameœ:œresponseœ,œidœ:œCleanlabRemediator-YB1Ssœ,œinputTypesœ:[œMessageœ],œtypeœ:œstrœ}", "selected": false, "source": "CleanlabEvaluator-Pfyl2", "sourceHandle": "{œdataTypeœ:œCleanlabEvaluatorœ,œidœ:œCleanlabEvaluator-Pfyl2œ,œnameœ:œresponse_passthroughœ,œoutput_typesœ:[œMessageœ]}", "target": "CleanlabRemediator-YB1Ss", "targetHandle": "{œfieldNameœ:œresponseœ,œidœ:œCleanlabRemediator-YB1Ssœ,œinputTypesœ:[œMessageœ],œtypeœ:œstrœ}"}], "nodes": [{"data": {"description": "Get chat inputs from the Playground.", "display_name": "Chat Input", "id": "ChatInput-wk8QV", "node": {"base_classes": ["Message"], "beta": false, "conditional_paths": [], "custom_fields": {}, "description": "Get chat inputs from the Playground.", "display_name": "Chat Input", "documentation": "", "edited": false, "field_order": ["input_value", "store_message", "sender", "sender_name", "session_id", "files"], "frozen": false, "icon": "MessagesSquare", "legacy": false, "lf_version": "1.4.1", "metadata": {}, "output_types": [], "outputs": [{"allows_loop": false, "cache": true, "display_name": "Message", "hidden": false, "method": "message_response", "name": "message", "selected": "Message", "tool_mode": true, "types": ["Message"], "value": "__UNDEFINED__"}], "pinned": false, "template": {"_type": "Component", "background_color": {"_input_type": "MessageTextInput", "advanced": true, "display_name": "Background Color", "dynamic": false, "info": "The background color of the icon.", "input_types": ["Message"], "list": false, "load_from_db": false, "name": "background_color", "placeholder": "", "required": false, "show": true, "title_case": false, "trace_as_input": true, "trace_as_metadata": true, "type": "str", "value": ""}, "chat_icon": {"_input_type": "MessageTextInput", "advanced": true, "display_name": "Icon", "dynamic": false, "info": "The icon of the message.", "input_types": ["Message"], "list": false, "load_from_db": false, "name": "chat_icon", "placeholder": "", "required": false, "show": true, "title_case": false, "trace_as_input": true, "trace_as_metadata": true, "type": "str", "value": ""}, "code": {"advanced": true, "dynamic": true, "fileTypes": [], "file_path": "", "info": "", "list": false, "load_from_db": false, "multiline": true, "name": "code", "password": false, "placeholder": "", "required": true, "show": true, "title_case": false, "type": "code", "value": "from langflow.base.data.utils import IMG_FILE_TYPES, TEXT_FILE_TYPES\nfrom langflow.base.io.chat import ChatComponent\nfrom langflow.inputs import BoolInput\nfrom langflow.io import (\n    DropdownInput,\n    FileInput,\n    MessageTextInput,\n    MultilineInput,\n    Output,\n)\nfrom langflow.schema.message import Message\nfrom langflow.utils.constants import (\n    MESSAGE_SENDER_AI,\n    MESSAGE_SENDER_NAME_USER,\n    MESSAGE_SENDER_USER,\n)\n\n\nclass ChatInput(ChatComponent):\n    display_name = \"Chat Input\"\n    description = \"Get chat inputs from the Playground.\"\n    icon = \"MessagesSquare\"\n    name = \"ChatInput\"\n    minimized = True\n\n    inputs = [\n        MultilineInput(\n            name=\"input_value\",\n            display_name=\"Text\",\n            value=\"\",\n            info=\"Message to be passed as input.\",\n            input_types=[],\n        ),\n        BoolInput(\n            name=\"should_store_message\",\n            display_name=\"Store Messages\",\n            info=\"Store the message in the history.\",\n            value=True,\n            advanced=True,\n        ),\n        DropdownInput(\n            name=\"sender\",\n            display_name=\"Sender Type\",\n            options=[MESSAGE_SENDER_AI, MESSAGE_SENDER_USER],\n            value=MESSAGE_SENDER_USER,\n            info=\"Type of sender.\",\n            advanced=True,\n        ),\n        MessageTextInput(\n            name=\"sender_name\",\n            display_name=\"Sender Name\",\n            info=\"Name of the sender.\",\n            value=MESSAGE_SENDER_NAME_USER,\n            advanced=True,\n        ),\n        MessageTextInput(\n            name=\"session_id\",\n            display_name=\"Session ID\",\n            info=\"The session ID of the chat. If empty, the current session ID parameter will be used.\",\n            advanced=True,\n        ),\n        FileInput(\n            name=\"files\",\n            display_name=\"Files\",\n            file_types=TEXT_FILE_TYPES + IMG_FILE_TYPES,\n            info=\"Files to be sent with the message.\",\n            advanced=True,\n            is_list=True,\n            temp_file=True,\n        ),\n        MessageTextInput(\n            name=\"background_color\",\n            display_name=\"Background Color\",\n            info=\"The background color of the icon.\",\n            advanced=True,\n        ),\n        MessageTextInput(\n            name=\"chat_icon\",\n            display_name=\"Icon\",\n            info=\"The icon of the message.\",\n            advanced=True,\n        ),\n        MessageTextInput(\n            name=\"text_color\",\n            display_name=\"Text Color\",\n            info=\"The text color of the name\",\n            advanced=True,\n        ),\n    ]\n    outputs = [\n        Output(display_name=\"Message\", name=\"message\", method=\"message_response\"),\n    ]\n\n    async def message_response(self) -> Message:\n        background_color = self.background_color\n        text_color = self.text_color\n        icon = self.chat_icon\n\n        message = await Message.create(\n            text=self.input_value,\n            sender=self.sender,\n            sender_name=self.sender_name,\n            session_id=self.session_id,\n            files=self.files,\n            properties={\n                \"background_color\": background_color,\n                \"text_color\": text_color,\n                \"icon\": icon,\n            },\n        )\n        if self.session_id and isinstance(message, Message) and self.should_store_message:\n            stored_message = await self.send_message(\n                message,\n            )\n            self.message.value = stored_message\n            message = stored_message\n\n        self.status = message\n        return message\n"}, "files": {"advanced": true, "display_name": "Files", "dynamic": false, "fileTypes": ["txt", "md", "mdx", "csv", "json", "yaml", "yml", "xml", "html", "htm", "pdf", "docx", "py", "sh", "sql", "js", "ts", "tsx", "jpg", "jpeg", "png", "bmp", "image"], "file_path": "", "info": "Files to be sent with the message.", "list": true, "name": "files", "placeholder": "", "required": false, "show": true, "temp_file": true, "title_case": false, "trace_as_metadata": true, "type": "file", "value": ""}, "input_value": {"advanced": false, "display_name": "Text", "dynamic": false, "info": "Message to be passed as input.", "input_types": [], "list": false, "load_from_db": false, "multiline": true, "name": "input_value", "placeholder": "", "required": false, "show": true, "title_case": false, "trace_as_input": true, "trace_as_metadata": true, "type": "str", "value": "What is the 3rd month of the year alphabetically?"}, "sender": {"advanced": true, "display_name": "Sender Type", "dynamic": false, "info": "Type of sender.", "name": "sender", "options": ["Machine", "User"], "placeholder": "", "required": false, "show": true, "title_case": false, "trace_as_metadata": true, "type": "str", "value": "User"}, "sender_name": {"advanced": true, "display_name": "Sender Name", "dynamic": false, "info": "Name of the sender.", "input_types": ["Message"], "list": false, "load_from_db": false, "name": "sender_name", "placeholder": "", "required": false, "show": true, "title_case": false, "trace_as_input": true, "trace_as_metadata": true, "type": "str", "value": "User"}, "session_id": {"advanced": true, "display_name": "Session ID", "dynamic": false, "info": "The session ID of the chat. If empty, the current session ID parameter will be used.", "input_types": ["Message"], "list": false, "load_from_db": false, "name": "session_id", "placeholder": "", "required": false, "show": true, "title_case": false, "trace_as_input": true, "trace_as_metadata": true, "type": "str", "value": ""}, "should_store_message": {"_input_type": "BoolInput", "advanced": true, "display_name": "Store Messages", "dynamic": false, "info": "Store the message in the history.", "list": false, "name": "should_store_message", "placeholder": "", "required": false, "show": true, "title_case": false, "trace_as_metadata": true, "type": "bool", "value": true}, "text_color": {"_input_type": "MessageTextInput", "advanced": true, "display_name": "Text Color", "dynamic": false, "info": "The text color of the name", "input_types": ["Message"], "list": false, "load_from_db": false, "name": "text_color", "placeholder": "", "required": false, "show": true, "title_case": false, "trace_as_input": true, "trace_as_metadata": true, "type": "str", "value": ""}}}, "type": "ChatInput"}, "dragging": false, "height": 234, "id": "ChatInput-wk8QV", "measured": {"height": 234, "width": 320}, "position": {"x": 629.0324831254778, "y": 643.9011650405961}, "positionAbsolute": {"x": 689.5720422421635, "y": 765.155834131403}, "selected": false, "type": "genericNode", "width": 320}, {"data": {"description": "Create a prompt template with dynamic variables.", "display_name": "Prompt", "id": "Prompt-MabiH", "node": {"base_classes": ["Message"], "beta": false, "conditional_paths": [], "custom_fields": {"template": []}, "description": "Create a system prompt.\n", "display_name": "System Message", "documentation": "", "edited": false, "error": null, "field_order": ["template"], "frozen": false, "full_path": null, "icon": "prompts", "is_composition": null, "is_input": null, "is_output": null, "legacy": false, "lf_version": "1.4.1", "metadata": {}, "minimized": false, "name": "", "output_types": [], "outputs": [{"allows_loop": false, "cache": true, "display_name": "Prompt Message", "hidden": false, "method": "build_prompt", "name": "prompt", "options": null, "required_inputs": null, "selected": "Message", "tool_mode": true, "types": ["Message"], "value": "__UNDEFINED__"}], "pinned": false, "priority": null, "template": {"_type": "Component", "code": {"advanced": true, "dynamic": true, "fileTypes": [], "file_path": "", "info": "", "list": false, "load_from_db": false, "multiline": true, "name": "code", "password": false, "placeholder": "", "required": true, "show": true, "title_case": false, "type": "code", "value": "from langflow.base.prompts.api_utils import process_prompt_template\nfrom langflow.custom import Component\nfrom langflow.inputs.inputs import Defa<PERSON><PERSON><PERSON>pt<PERSON><PERSON>\nfrom langflow.io import MessageTextInput, Output, PromptInput\nfrom langflow.schema.message import Message\nfrom langflow.template.utils import update_template_values\n\n\nclass PromptComponent(Component):\n    display_name: str = \"Prompt\"\n    description: str = \"Create a prompt template with dynamic variables.\"\n    icon = \"prompts\"\n    trace_type = \"prompt\"\n    name = \"Prompt\"\n\n    inputs = [\n        PromptInput(name=\"template\", display_name=\"Template\"),\n        MessageTextInput(\n            name=\"tool_placeholder\",\n            display_name=\"Tool Placeholder\",\n            tool_mode=True,\n            advanced=True,\n            info=\"A placeholder input for tool mode.\",\n        ),\n    ]\n\n    outputs = [\n        Output(display_name=\"Prompt Message\", name=\"prompt\", method=\"build_prompt\"),\n    ]\n\n    async def build_prompt(self) -> Message:\n        prompt = Message.from_template(**self._attributes)\n        self.status = prompt.text\n        return prompt\n\n    def _update_template(self, frontend_node: dict):\n        prompt_template = frontend_node[\"template\"][\"template\"][\"value\"]\n        custom_fields = frontend_node[\"custom_fields\"]\n        frontend_node_template = frontend_node[\"template\"]\n        _ = process_prompt_template(\n            template=prompt_template,\n            name=\"template\",\n            custom_fields=custom_fields,\n            frontend_node_template=frontend_node_template,\n        )\n        return frontend_node\n\n    async def update_frontend_node(self, new_frontend_node: dict, current_frontend_node: dict):\n        \"\"\"This function is called after the code validation is done.\"\"\"\n        frontend_node = await super().update_frontend_node(new_frontend_node, current_frontend_node)\n        template = frontend_node[\"template\"][\"template\"][\"value\"]\n        # Kept it duplicated for backwards compatibility\n        _ = process_prompt_template(\n            template=template,\n            name=\"template\",\n            custom_fields=frontend_node[\"custom_fields\"],\n            frontend_node_template=frontend_node[\"template\"],\n        )\n        # Now that template is updated, we need to grab any values that were set in the current_frontend_node\n        # and update the frontend_node with those values\n        update_template_values(new_template=frontend_node, previous_template=current_frontend_node[\"template\"])\n        return frontend_node\n\n    def _get_fallback_input(self, **kwargs):\n        return DefaultPromptField(**kwargs)\n"}, "template": {"_input_type": "PromptInput", "advanced": false, "display_name": "Template", "dynamic": false, "info": "", "list": false, "load_from_db": false, "name": "template", "placeholder": "", "required": false, "show": true, "title_case": false, "tool_mode": false, "trace_as_input": true, "type": "prompt", "value": "You are a helpful assistant.\n"}, "tool_placeholder": {"_input_type": "MessageTextInput", "advanced": true, "display_name": "Tool Placeholder", "dynamic": false, "info": "A placeholder input for tool mode.", "input_types": ["Message"], "list": false, "load_from_db": false, "name": "tool_placeholder", "placeholder": "", "required": false, "show": true, "title_case": false, "tool_mode": true, "trace_as_input": true, "trace_as_metadata": true, "type": "str", "value": ""}}, "tool_mode": false}, "type": "Prompt"}, "dragging": false, "height": 260, "id": "Prompt-MabiH", "measured": {"height": 260, "width": 320}, "position": {"x": 624.7914648802054, "y": 958.3921915653085}, "positionAbsolute": {"x": 690.2015147036818, "y": 1018.5443911764344}, "selected": false, "type": "genericNode", "width": 320}, {"data": {"id": "OpenAIModel-1P8nP", "node": {"base_classes": ["LanguageModel", "Message"], "beta": false, "conditional_paths": [], "custom_fields": {}, "description": "Generates text using OpenAI LLMs.", "display_name": "OpenAI", "documentation": "", "edited": false, "field_order": ["input_value", "system_message", "stream", "max_tokens", "model_kwargs", "json_mode", "model_name", "openai_api_base", "api_key", "temperature", "seed", "max_retries", "timeout"], "frozen": false, "icon": "OpenAI", "legacy": false, "lf_version": "1.4.1", "metadata": {}, "minimized": false, "output_types": [], "outputs": [{"allows_loop": false, "cache": true, "display_name": "Message", "hidden": false, "method": "text_response", "name": "text_output", "required_inputs": [], "selected": "Message", "tool_mode": true, "types": ["Message"], "value": "__UNDEFINED__"}, {"allows_loop": false, "cache": true, "display_name": "Language Model", "method": "build_model", "name": "model_output", "required_inputs": ["api_key"], "selected": "LanguageModel", "tool_mode": true, "types": ["LanguageModel"], "value": "__UNDEFINED__"}], "pinned": false, "template": {"_type": "Component", "api_key": {"_input_type": "SecretStrInput", "advanced": false, "display_name": "OpenAI API Key", "dynamic": false, "info": "The OpenAI API Key to use for the OpenAI model.", "input_types": [], "load_from_db": false, "name": "api_key", "password": true, "placeholder": "", "required": true, "show": true, "title_case": false, "type": "str", "value": ""}, "code": {"advanced": true, "dynamic": true, "fileTypes": [], "file_path": "", "info": "", "list": false, "load_from_db": false, "multiline": true, "name": "code", "password": false, "placeholder": "", "required": true, "show": true, "title_case": false, "type": "code", "value": "from typing import Any\n\nfrom langchain_openai import ChatOpenAI\nfrom pydantic.v1 import SecretStr\n\nfrom langflow.base.models.model import LCModelComponent\nfrom langflow.base.models.openai_constants import (\n    OPENAI_MODEL_NAMES,\n    OPENAI_REASONING_MODEL_NAMES,\n)\nfrom langflow.field_typing import LanguageModel\nfrom langflow.field_typing.range_spec import RangeSpec\nfrom langflow.inputs import BoolInput, DictInput, DropdownInput, IntInput, SecretStrInput, SliderInput, StrInput\nfrom langflow.logging import logger\n\n\nclass OpenAIModelComponent(LCModelComponent):\n    display_name = \"OpenAI\"\n    description = \"Generates text using OpenAI LLMs.\"\n    icon = \"OpenAI\"\n    name = \"OpenAIModel\"\n\n    inputs = [\n        *LCModelComponent._base_inputs,\n        IntInput(\n            name=\"max_tokens\",\n            display_name=\"Max Tokens\",\n            advanced=True,\n            info=\"The maximum number of tokens to generate. Set to 0 for unlimited tokens.\",\n            range_spec=RangeSpec(min=0, max=128000),\n        ),\n        DictInput(\n            name=\"model_kwargs\",\n            display_name=\"Model Kwargs\",\n            advanced=True,\n            info=\"Additional keyword arguments to pass to the model.\",\n        ),\n        BoolInput(\n            name=\"json_mode\",\n            display_name=\"JSON Mode\",\n            advanced=True,\n            info=\"If True, it will output JSON regardless of passing a schema.\",\n        ),\n        DropdownInput(\n            name=\"model_name\",\n            display_name=\"Model Name\",\n            advanced=False,\n            options=OPENAI_MODEL_NAMES + OPENAI_REASONING_MODEL_NAMES,\n            value=OPENAI_MODEL_NAMES[1],\n            combobox=True,\n            real_time_refresh=True,\n        ),\n        StrInput(\n            name=\"openai_api_base\",\n            display_name=\"OpenAI API Base\",\n            advanced=True,\n            info=\"The base URL of the OpenAI API. \"\n            \"Defaults to https://api.openai.com/v1. \"\n            \"You can change this to use other APIs like JinaChat, LocalAI and Prem.\",\n        ),\n        SecretStrInput(\n            name=\"api_key\",\n            display_name=\"OpenAI API Key\",\n            info=\"The OpenAI API Key to use for the OpenAI model.\",\n            advanced=False,\n            value=\"OPENAI_API_KEY\",\n            required=True,\n        ),\n        SliderInput(\n            name=\"temperature\",\n            display_name=\"Temperature\",\n            value=0.1,\n            range_spec=RangeSpec(min=0, max=1, step=0.01),\n            show=True,\n        ),\n        IntInput(\n            name=\"seed\",\n            display_name=\"Seed\",\n            info=\"The seed controls the reproducibility of the job.\",\n            advanced=True,\n            value=1,\n        ),\n        IntInput(\n            name=\"max_retries\",\n            display_name=\"Max Retries\",\n            info=\"The maximum number of retries to make when generating.\",\n            advanced=True,\n            value=5,\n        ),\n        IntInput(\n            name=\"timeout\",\n            display_name=\"Timeout\",\n            info=\"The timeout for requests to OpenAI completion API.\",\n            advanced=True,\n            value=700,\n        ),\n    ]\n\n    def build_model(self) -> LanguageModel:  # type: ignore[type-var]\n        parameters = {\n            \"api_key\": SecretStr(self.api_key).get_secret_value() if self.api_key else None,\n            \"model_name\": self.model_name,\n            \"max_tokens\": self.max_tokens or None,\n            \"model_kwargs\": self.model_kwargs or {},\n            \"base_url\": self.openai_api_base or \"https://api.openai.com/v1\",\n            \"seed\": self.seed,\n            \"max_retries\": self.max_retries,\n            \"timeout\": self.timeout,\n            \"temperature\": self.temperature if self.temperature is not None else 0.1,\n        }\n\n        logger.info(f\"Model name: {self.model_name}\")\n        if self.model_name in OPENAI_REASONING_MODEL_NAMES:\n            logger.info(\"Getting reasoning model parameters\")\n            parameters.pop(\"temperature\")\n            parameters.pop(\"seed\")\n        output = ChatOpenAI(**parameters)\n        if self.json_mode:\n            output = output.bind(response_format={\"type\": \"json_object\"})\n\n        return output\n\n    def _get_exception_message(self, e: Exception):\n        \"\"\"Get a message from an OpenAI exception.\n\n        Args:\n            e (Exception): The exception to get the message from.\n\n        Returns:\n            str: The message from the exception.\n        \"\"\"\n        try:\n            from openai import BadRequestError\n        except ImportError:\n            return None\n        if isinstance(e, BadRequestError):\n            message = e.body.get(\"message\")\n            if message:\n                return message\n        return None\n\n    def update_build_config(self, build_config: dict, field_value: Any, field_name: str | None = None) -> dict:\n        if field_name in {\"base_url\", \"model_name\", \"api_key\"} and field_value in OPENAI_REASONING_MODEL_NAMES:\n            build_config[\"temperature\"][\"show\"] = False\n            build_config[\"seed\"][\"show\"] = False\n        if field_name in {\"base_url\", \"model_name\", \"api_key\"} and field_value in OPENAI_MODEL_NAMES:\n            build_config[\"temperature\"][\"show\"] = True\n            build_config[\"seed\"][\"show\"] = True\n        return build_config\n"}, "input_value": {"_input_type": "MessageInput", "advanced": false, "display_name": "Input", "dynamic": false, "info": "", "input_types": ["Message"], "list": false, "list_add_label": "Add More", "load_from_db": false, "name": "input_value", "placeholder": "", "required": false, "show": true, "title_case": false, "tool_mode": false, "trace_as_input": true, "trace_as_metadata": true, "type": "str", "value": ""}, "json_mode": {"_input_type": "BoolInput", "advanced": true, "display_name": "JSON Mode", "dynamic": false, "info": "If True, it will output JSON regardless of passing a schema.", "list": false, "list_add_label": "Add More", "name": "json_mode", "placeholder": "", "required": false, "show": true, "title_case": false, "tool_mode": false, "trace_as_metadata": true, "type": "bool", "value": false}, "max_retries": {"_input_type": "IntInput", "advanced": true, "display_name": "Max Retries", "dynamic": false, "info": "The maximum number of retries to make when generating.", "list": false, "list_add_label": "Add More", "name": "max_retries", "placeholder": "", "required": false, "show": true, "title_case": false, "tool_mode": false, "trace_as_metadata": true, "type": "int", "value": 5}, "max_tokens": {"_input_type": "IntInput", "advanced": true, "display_name": "<PERSON>", "dynamic": false, "info": "The maximum number of tokens to generate. Set to 0 for unlimited tokens.", "list": false, "list_add_label": "Add More", "name": "max_tokens", "placeholder": "", "range_spec": {"max": 128000, "min": 0, "step": 0.1, "step_type": "float"}, "required": false, "show": true, "title_case": false, "tool_mode": false, "trace_as_metadata": true, "type": "int", "value": ""}, "model_kwargs": {"_input_type": "DictInput", "advanced": true, "display_name": "Model Kwargs", "dynamic": false, "info": "Additional keyword arguments to pass to the model.", "list": false, "list_add_label": "Add More", "name": "model_kwargs", "placeholder": "", "required": false, "show": true, "title_case": false, "tool_mode": false, "trace_as_input": true, "type": "dict", "value": {}}, "model_name": {"_input_type": "DropdownInput", "advanced": false, "combobox": true, "dialog_inputs": {}, "display_name": "Model Name", "dynamic": false, "info": "", "name": "model_name", "options": ["gpt-4o-mini", "gpt-4o", "gpt-4.1", "gpt-4.1-mini", "gpt-4.1-nano", "gpt-4.5-preview", "gpt-4-turbo", "gpt-4-turbo-preview", "gpt-4", "gpt-3.5-turbo", "o1"], "options_metadata": [], "placeholder": "", "required": false, "show": true, "title_case": false, "tool_mode": false, "trace_as_metadata": true, "type": "str", "value": "gpt-4o"}, "openai_api_base": {"_input_type": "StrInput", "advanced": true, "display_name": "OpenAI API Base", "dynamic": false, "info": "The base URL of the OpenAI API. Defaults to https://api.openai.com/v1. You can change this to use other APIs like JinaChat, LocalAI and Prem.", "list": false, "list_add_label": "Add More", "load_from_db": false, "name": "openai_api_base", "placeholder": "", "required": false, "show": true, "title_case": false, "tool_mode": false, "trace_as_metadata": true, "type": "str", "value": ""}, "seed": {"_input_type": "IntInput", "advanced": true, "display_name": "Seed", "dynamic": false, "info": "The seed controls the reproducibility of the job.", "list": false, "list_add_label": "Add More", "name": "seed", "placeholder": "", "required": false, "show": true, "title_case": false, "tool_mode": false, "trace_as_metadata": true, "type": "int", "value": 1}, "stream": {"_input_type": "BoolInput", "advanced": true, "display_name": "Stream", "dynamic": false, "info": "Stream the response from the model. Streaming works only in Chat.", "list": false, "list_add_label": "Add More", "name": "stream", "placeholder": "", "required": false, "show": true, "title_case": false, "tool_mode": false, "trace_as_metadata": true, "type": "bool", "value": false}, "system_message": {"_input_type": "MultilineInput", "advanced": false, "display_name": "System Message", "dynamic": false, "info": "System message to pass to the model.", "input_types": ["Message"], "list": false, "list_add_label": "Add More", "load_from_db": false, "multiline": true, "name": "system_message", "placeholder": "", "required": false, "show": true, "title_case": false, "tool_mode": false, "trace_as_input": true, "trace_as_metadata": true, "type": "str", "value": ""}, "temperature": {"_input_type": "SliderInput", "advanced": false, "display_name": "Temperature", "dynamic": false, "info": "", "load_from_db": false, "max_label": "", "max_label_icon": "", "min_label": "", "min_label_icon": "", "name": "temperature", "placeholder": "", "range_spec": {"max": 1, "min": 0, "step": 0.01, "step_type": "float"}, "required": false, "show": true, "slider_buttons": false, "slider_buttons_options": [], "slider_input": false, "title_case": false, "tool_mode": false, "type": "slider", "value": 0.2}, "timeout": {"_input_type": "IntInput", "advanced": true, "display_name": "Timeout", "dynamic": false, "info": "The timeout for requests to OpenAI completion API.", "list": false, "list_add_label": "Add More", "name": "timeout", "placeholder": "", "required": false, "show": true, "title_case": false, "tool_mode": false, "trace_as_metadata": true, "type": "int", "value": 700}}, "tool_mode": false}, "showNode": true, "type": "OpenAIModel"}, "dragging": false, "id": "OpenAIModel-1P8nP", "measured": {"height": 614, "width": 320}, "position": {"x": 1070.7203298910767, "y": 624.1515018284463}, "selected": false, "type": "genericNode"}, {"data": {"id": "CleanlabEvaluator-Pfyl2", "node": {"base_classes": ["float", "Message", "number"], "beta": false, "conditional_paths": [], "custom_fields": {}, "description": "Evaluates any LLM response using Cleanlab and outputs trust score and explanation.", "display_name": "Cleanlab Evaluator", "documentation": "", "edited": false, "field_order": ["system_prompt", "prompt", "response", "api_key", "model", "quality_preset"], "frozen": false, "icon": "Cleanlab", "legacy": false, "lf_version": "1.4.1", "metadata": {}, "minimized": false, "output_types": [], "outputs": [{"allows_loop": false, "cache": true, "display_name": "Response", "hidden": false, "method": "pass_response", "name": "response_passthrough", "options": null, "required_inputs": null, "selected": "Message", "tool_mode": true, "types": ["Message"], "value": "__UNDEFINED__"}, {"allows_loop": false, "cache": true, "display_name": "Trust Score", "hidden": false, "method": "get_score", "name": "score", "options": null, "required_inputs": null, "selected": "number", "tool_mode": true, "types": ["number", "float"], "value": "__UNDEFINED__"}, {"allows_loop": false, "cache": true, "display_name": "Explanation", "hidden": false, "method": "get_explanation", "name": "explanation", "options": null, "required_inputs": null, "selected": "Message", "tool_mode": true, "types": ["Message"], "value": "__UNDEFINED__"}], "pinned": false, "template": {"_type": "Component", "api_key": {"_input_type": "SecretStrInput", "advanced": false, "display_name": "Cleanlab API Key", "dynamic": false, "info": "Your Cleanlab API key.", "input_types": [], "load_from_db": false, "name": "api_key", "password": true, "placeholder": "", "required": true, "show": true, "title_case": false, "type": "str", "value": ""}, "code": {"advanced": true, "dynamic": true, "fileTypes": [], "file_path": "", "info": "", "list": false, "load_from_db": false, "multiline": true, "name": "code", "password": false, "placeholder": "", "required": true, "show": true, "title_case": false, "type": "code", "value": "from langflow.custom import Component\nfrom langflow.io import (\n    MessageTextInput,\n    Output,\n    SecretStrInput,\n    DropdownInput,\n)\nfrom langflow.schema.message import Message\nfrom cleanlab_tlm import TLM\n\n\nclass CleanlabEvaluator(Component):\n    \"\"\"\n    A component that evaluates the trustworthiness of LLM responses using Cleanlab.\n\n    This component takes a prompt and response pair, along with optional system instructions,\n    and uses Cleanlab's evaluation algorithms to generate a trust score and explanation.\n\n    Inputs:\n        - system_prompt (MessageTextInput): Optional system-level instructions prepended to the user prompt.\n        - prompt (MessageTextInput): The user's prompt or query sent to the LLM.\n        - response (MessageTextInput): The response generated by the LLM to be evaluated. This should come from the LLM component, i.e. OpenAI, Gemini, etc.\n        - api_key (SecretStrInput): Your Cleanlab API key.\n        - model (DropdownInput): The model used by Cleanlab to evaluate the response (can differ from the generation model).\n        - quality_preset (DropdownInput): Tradeoff setting for accuracy vs. speed and cost. Higher presets are slower but more accurate.\n\n    Outputs:\n        - response_passthrough (Message): The original response, passed through for downstream use.\n        - score (number): A float between 0 and 1 indicating Cleanlab's trustworthiness score for the response.\n        - explanation (Message): A textual explanation of why the response received its score.\n\n    This component works well in conjunction with the CleanlabRemediator to create a complete trust evaluation and remediation pipeline.\n\n    More details on the evaluation metrics can be found here: https://help.cleanlab.ai/tlm/tutorials/tlm/\n    \"\"\"\n\n    display_name = \"Cleanlab Evaluator\"\n    description = \"Evaluates any LLM response using Cleanlab and outputs trust score and explanation.\"\n    icon = \"Cleanlab\"\n    name = \"CleanlabEvaluator\"\n\n    inputs = [\n        MessageTextInput(\n            name=\"system_prompt\",\n            display_name=\"System Message\",\n            info=\"System-level instructions prepended to the user query.\",\n            value=\"\",\n        ),\n        MessageTextInput(\n            name=\"prompt\",\n            display_name=\"Prompt\",\n            info=\"The user's query to the model.\",\n            required=True,\n        ),\n        MessageTextInput(\n            name=\"response\",\n            display_name=\"Response\",\n            info=\"The response to the user's query.\",\n            required=True,\n        ),\n        SecretStrInput(\n            name=\"api_key\",\n            display_name=\"Cleanlab API Key\",\n            info=\"Your Cleanlab API key.\",\n            required=True,\n        ),\n        DropdownInput(\n            name=\"model\",\n            display_name=\"Cleanlab Evaluation Model\",\n            options=[\n                \"gpt-4.1\", \"gpt-4.1-mini\", \"gpt-4.1-nano\", \"o4-mini\", \"o3\",\n                \"gpt-4.5-preview\", \"gpt-4o-mini\", \"gpt-4o\", \"o3-mini\", \"o1\", \"o1-mini\",\n                \"gpt-4\", \"gpt-3.5-turbo-16k\",\n                \"claude-3.7-sonnet\", \"claude-3.5-sonnet-v2\", \"claude-3.5-sonnet\",\n                \"claude-3.5-haiku\", \"claude-3-haiku\",\n                \"nova-micro\", \"nova-lite\", \"nova-pro\"\n            ],\n            info=\"The model Cleanlab uses to evaluate the response. This does NOT need to be the same model that generated the response.\",\n            value=\"gpt-4o-mini\",\n            required=True,\n            advanced=True,\n        ),\n        DropdownInput(\n            name=\"quality_preset\",\n            display_name=\"Quality Preset\",\n            options=[\"base\", \"low\", \"medium\", \"high\", \"best\"],\n            value=\"medium\",\n            info=\"This determines the accuracy, latency, and cost of the evaluation. Higher quality is generally slower but more accurate.\",\n            required=True,\n            advanced=True,\n        ),\n    ]\n\n    outputs = [\n        Output(display_name=\"Response\", name=\"response_passthrough\", method=\"pass_response\", types=[\"Message\"]),\n        Output(display_name=\"Trust Score\", name=\"score\", method=\"get_score\", types=[\"number\"]),\n        Output(display_name=\"Explanation\", name=\"explanation\", method=\"get_explanation\", types=[\"Message\"]),\n    ]\n\n    def _evaluate_once(self):\n        if not hasattr(self, \"_cached_result\"):\n            full_prompt = f\"{self.system_prompt}\\n\\n{self.prompt}\" if self.system_prompt else self.prompt\n            tlm = TLM(\n                api_key=self.api_key,\n                options={\"log\": [\"explanation\"], \"model\": self.model},\n                quality_preset=self.quality_preset,\n            )\n            self._cached_result = tlm.get_trustworthiness_score(full_prompt, self.response)\n        return self._cached_result\n\n    def get_score(self) -> float:\n        result = self._evaluate_once()\n        score = result.get(\"trustworthiness_score\", 0.0)\n        self.status = f\"Trust score: {score:.2f}\"\n        return score\n\n    def get_explanation(self) -> Message:\n        result = self._evaluate_once()\n        explanation = result.get(\"log\", {}).get(\"explanation\", \"No explanation returned.\")\n        return Message(text=explanation)\n    \n    def pass_response(self) -> Message:\n        self.status = \"Passing through response.\"\n        return Message(text=self.response)"}, "model": {"_input_type": "DropdownInput", "advanced": true, "combobox": false, "dialog_inputs": {}, "display_name": "Cleanlab Evaluation Model", "dynamic": false, "info": "The model Cleanlab uses to evaluate the response. This does NOT need to be the same model that generated the response.", "name": "model", "options": ["gpt-4.1", "gpt-4.1-mini", "gpt-4.1-nano", "o4-mini", "o3", "gpt-4.5-preview", "gpt-4o-mini", "gpt-4o", "o3-mini", "o1", "o1-mini", "gpt-4", "gpt-3.5-turbo-16k", "claude-3.7-sonnet", "claude-3.5-sonnet-v2", "claude-3.5-sonnet", "claude-3.5-haiku", "claude-3-haiku", "nova-micro", "nova-lite", "nova-pro"], "options_metadata": [], "placeholder": "", "required": true, "show": true, "title_case": false, "toggle": false, "tool_mode": false, "trace_as_metadata": true, "type": "str", "value": "gpt-4o-mini"}, "prompt": {"_input_type": "MessageTextInput", "advanced": false, "display_name": "Prompt", "dynamic": false, "info": "The user's query to the model.", "input_types": ["Message"], "list": false, "list_add_label": "Add More", "load_from_db": false, "name": "prompt", "placeholder": "", "required": true, "show": true, "title_case": false, "tool_mode": false, "trace_as_input": true, "trace_as_metadata": true, "type": "str", "value": ""}, "quality_preset": {"_input_type": "DropdownInput", "advanced": true, "combobox": false, "dialog_inputs": {}, "display_name": "Quality Preset", "dynamic": false, "info": "This determines the accuracy, latency, and cost of the evaluation. Higher quality is generally slower but more accurate.", "name": "quality_preset", "options": ["base", "low", "medium", "high", "best"], "options_metadata": [], "placeholder": "", "required": true, "show": true, "title_case": false, "toggle": false, "tool_mode": false, "trace_as_metadata": true, "type": "str", "value": "medium"}, "response": {"_input_type": "MessageTextInput", "advanced": false, "display_name": "Response", "dynamic": false, "info": "The response to the user's query.", "input_types": ["Message"], "list": false, "list_add_label": "Add More", "load_from_db": false, "name": "response", "placeholder": "", "required": true, "show": true, "title_case": false, "tool_mode": false, "trace_as_input": true, "trace_as_metadata": true, "type": "str", "value": ""}, "system_prompt": {"_input_type": "MessageTextInput", "advanced": false, "display_name": "System Message", "dynamic": false, "info": "System-level instructions prepended to the user query.", "input_types": ["Message"], "list": false, "list_add_label": "Add More", "load_from_db": false, "name": "system_prompt", "placeholder": "", "required": false, "show": true, "title_case": false, "tool_mode": false, "trace_as_input": true, "trace_as_metadata": true, "type": "str", "value": ""}}, "tool_mode": false}, "showNode": true, "type": "CleanlabEvaluator"}, "dragging": false, "id": "CleanlabEvaluator-Pfyl2", "measured": {"height": 593, "width": 320}, "position": {"x": 1471.9658869078787, "y": 640.3964922751719}, "selected": false, "type": "genericNode"}, {"data": {"id": "CleanlabRemediator-YB1Ss", "node": {"base_classes": ["Message"], "beta": false, "conditional_paths": [], "custom_fields": {}, "description": "Remediates an untrustworthy response based on trust score from the Cleanlab Evaluator, score threshold, and message handling settings.", "display_name": "Cleanlab Remediator", "documentation": "", "edited": true, "field_order": ["response", "score", "explanation", "threshold", "show_untrustworthy_response", "untrustworthy_warning_text", "fallback_text"], "frozen": false, "icon": "Cleanlab", "legacy": false, "lf_version": "1.4.1", "metadata": {}, "minimized": false, "output_types": [], "outputs": [{"allows_loop": false, "cache": true, "display_name": "Remediated Message", "hidden": false, "method": "remediate_response", "name": "remediated_response", "options": null, "required_inputs": null, "selected": "Message", "tool_mode": true, "types": ["Message"], "value": "__UNDEFINED__"}], "pinned": false, "template": {"_type": "Component", "code": {"advanced": true, "dynamic": true, "fileTypes": [], "file_path": "", "info": "", "list": false, "load_from_db": false, "multiline": true, "name": "code", "password": false, "placeholder": "", "required": true, "show": true, "title_case": false, "type": "code", "value": "from langflow.custom import Component\nfrom langflow.io import (\n    MessageTextInput,\n    PromptInput,\n    Output,\n    BoolInput,\n    HandleInput,\n    FloatInput\n)\nfrom langflow.template import Input\nfrom langflow.schema.message import Message\nfrom langflow.field_typing.range_spec import RangeSpec\n\n\nclass CleanlabRemediator(Component):\n    \"\"\"\n    A component that remediates potentially untrustworthy LLM responses based on trust scores computed by the Cleanlab Evaluator.\n\n    This component takes a response and its associated trust score,\n    and applies remediation strategies based on configurable thresholds and settings.\n\n    Inputs:\n        - response (MessageTextInput): The original LLM-generated response to be evaluated and possibly remediated. The CleanlabEvaluator passes this response through.\n        - score (HandleInput): The trust score output from CleanlabEvaluator (expected to be a float between 0 and 1).\n        - explanation (MessageTextInput): Optional textual explanation for the trust score, to be included in the output.\n        - threshold (Input[float]): Minimum trust score required to accept the response. If the score is lower, the response is remediated.\n        - show_untrustworthy_response (BoolInput): If true, returns the original response with a warning; if false, returns fallback text.\n        - untrustworthy_warning_text (PromptInput): Text warning to append to responses deemed untrustworthy (when showing them).\n        - fallback_text (PromptInput): Replacement message returned if the response is untrustworthy and should be hidden.\n\n    Outputs:\n        - remediated_response (Message): Either:\n            • the original response,\n            • the original response with appended warning, or\n            • the fallback response,\n          depending on the trust score and configuration.\n\n    This component is typically used downstream of CleanlabEvaluator or CleanlabRagValidator\n    to take appropriate action on low-trust responses and inform users accordingly.\n    \"\"\"\n    \n    display_name = \"Cleanlab Remediator\"\n    description = \"Remediates an untrustworthy response based on trust score from the Cleanlab Evaluator, score threshold, and message handling settings.\"\n    icon = \"Cleanlab\"\n    name = \"CleanlabRemediator\"\n\n    inputs = [\n        MessageTextInput(\n            name=\"response\",\n            display_name=\"Response\",\n            info=\"The response to the user's query.\",\n            required=True,\n        ),\n        HandleInput(\n            name=\"score\",\n            display_name=\"Trust Score\",\n            info=\"The trustworthiness score output from the Cleanlab Evaluator.\",\n            input_types=[\"number\"],\n            required=True,\n        ),\n        MessageTextInput(\n            name=\"explanation\",\n            display_name=\"Explanation\",\n            info=\"The explanation from the Cleanlab Evaluator.\",\n            required=False,\n        ),\n        FloatInput(\n            name=\"threshold\",\n            display_name=\"Threshold\",\n            field_type=\"float\",\n            value=0.7,\n            range_spec=RangeSpec(min=0.0, max=1.0, step=0.05),\n            info=\"Minimum score required to show the response unmodified. Reponses with scores above this threshold are considered trustworthy. Reponses with scores below this threshold are considered untrustworthy and will be remediated based on the settings below.\",\n            required=True,\n            show=True,\n        ),\n        BoolInput(\n            name=\"show_untrustworthy_response\",\n            display_name=\"Show Untrustworthy Response\",\n            info=\"If enabled, and the trust score is below the threshold, the original response is shown with the added warning. If disabled, and the trust score is below the threshold, the fallback answer is returned.\",\n            value=True,\n        ),\n        PromptInput(\n            name=\"untrustworthy_warning_text\",\n            display_name=\"Warning for Untrustworthy Response\",\n            info=\"Warning to append to the response if Show Untrustworthy Response is enabled and trust score is below the threshold.\",\n            value=\"⚠️ WARNING: The following response is potentially untrustworthy.\",\n        ),\n        PromptInput(\n            name=\"fallback_text\",\n            display_name=\"Fallback Answer\",\n            info=\"Response returned if the trust score is below the threshold and 'Show Untrustworthy Response' is disabled.\",\n            value=\"Based on the available information, I cannot provide a complete answer to this question.\",\n        ),\n    ]\n\n    outputs = [\n        Output(display_name=\"Remediated Message\", name=\"remediated_response\", method=\"remediate_response\", types=[\"Message\"]),\n    ]\n    \n    def remediate_response(self) -> Message:\n        if self.score >= self.threshold:\n            self.status = f\"Score {self.score:.2f} ≥ threshold {self.threshold:.2f} → accepted\"\n            return Message(\n                text=f\"{self.response}\\n\\n━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\\n\\n**Trust Score:** {self.score:.2f}\"\n            )\n    \n        self.status = f\"Score {self.score:.2f} < threshold {self.threshold:.2f} → flagged\"\n    \n        if self.show_untrustworthy_response:\n            parts = [\n                self.response,\n                \"━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\",\n                f\"**{self.untrustworthy_warning_text.strip()}**\",\n                f\"**Trust Score:** {self.score:.2f}\",\n            ]\n            if self.explanation:\n                parts.append(f\"**Explanation:** {self.explanation}\")\n            return Message(text=\"\\n\\n\".join(parts))\n    \n        return Message(text=self.fallback_text)\n"}, "explanation": {"_input_type": "MessageTextInput", "advanced": false, "display_name": "Explanation", "dynamic": false, "info": "The explanation from the Cleanlab Evaluator.", "input_types": ["Message"], "list": false, "list_add_label": "Add More", "load_from_db": false, "name": "explanation", "placeholder": "", "required": false, "show": true, "title_case": false, "tool_mode": false, "trace_as_input": true, "trace_as_metadata": true, "type": "str", "value": ""}, "fallback_text": {"_input_type": "PromptInput", "advanced": false, "display_name": "Fallback Answer", "dynamic": false, "info": "Response returned if the trust score is below the threshold and 'Show Untrustworthy Response' is disabled.", "list": false, "list_add_label": "Add More", "name": "fallback_text", "placeholder": "", "required": false, "show": true, "title_case": false, "tool_mode": false, "trace_as_input": true, "type": "prompt", "value": "Based on the available information, I cannot provide a complete answer to this question."}, "response": {"_input_type": "MessageTextInput", "advanced": false, "display_name": "Response", "dynamic": false, "info": "The response to the user's query.", "input_types": ["Message"], "list": false, "list_add_label": "Add More", "load_from_db": false, "name": "response", "placeholder": "", "required": true, "show": true, "title_case": false, "tool_mode": false, "trace_as_input": true, "trace_as_metadata": true, "type": "str", "value": ""}, "score": {"_input_type": "HandleInput", "advanced": false, "display_name": "Trust Score", "dynamic": false, "info": "The trustworthiness score output from the Cleanlab Evaluator.", "input_types": ["number"], "list": false, "list_add_label": "Add More", "name": "score", "placeholder": "", "required": true, "show": true, "title_case": false, "trace_as_metadata": true, "type": "other", "value": ""}, "show_untrustworthy_response": {"_input_type": "BoolInput", "advanced": false, "display_name": "Show Untrustworthy Response", "dynamic": false, "info": "If enabled, and the trust score is below the threshold, the original response is shown with the added warning. If disabled, and the trust score is below the threshold, the fallback answer is returned.", "list": false, "list_add_label": "Add More", "name": "show_untrustworthy_response", "placeholder": "", "required": false, "show": true, "title_case": false, "tool_mode": false, "trace_as_metadata": true, "type": "bool", "value": true}, "threshold": {"_input_type": "FloatInput", "advanced": false, "display_name": "<PERSON><PERSON><PERSON><PERSON>", "dynamic": false, "info": "Minimum score required to show the response unmodified. Reponses with scores above this threshold are considered trustworthy. Reponses with scores below this threshold are considered untrustworthy and will be remediated based on the settings below.", "list": false, "list_add_label": "Add More", "load_from_db": false, "name": "threshold", "placeholder": "", "range_spec": {"max": 1, "min": 0, "step": 0.05, "step_type": "float"}, "required": true, "show": true, "title_case": false, "tool_mode": false, "trace_as_metadata": true, "type": "float", "value": 0.7}, "untrustworthy_warning_text": {"_input_type": "PromptInput", "advanced": false, "display_name": "Warning for Untrustworthy Response", "dynamic": false, "info": "Warning to append to the response if Show Untrustworthy Response is enabled and trust score is below the threshold.", "list": false, "list_add_label": "Add More", "name": "untrustworthy_warning_text", "placeholder": "", "required": false, "show": true, "title_case": false, "tool_mode": false, "trace_as_input": true, "type": "prompt", "value": "⚠️ WARNING: The following response is potentially untrustworthy."}}, "tool_mode": false}, "showNode": true, "type": "CleanlabRemediator"}, "dragging": false, "id": "CleanlabRemediator-YB1Ss", "measured": {"height": 744, "width": 320}, "position": {"x": 1901.0079628887015, "y": 571.0103984031506}, "selected": true, "type": "genericNode"}, {"data": {"id": "ChatOutput-9jsnO", "node": {"base_classes": ["Message"], "beta": false, "category": "outputs", "conditional_paths": [], "custom_fields": {}, "description": "Display a chat message in the Playground.", "display_name": "Chat Output", "documentation": "", "edited": false, "field_order": ["input_value", "should_store_message", "sender", "sender_name", "session_id", "data_template", "background_color", "chat_icon", "text_color", "clean_data"], "frozen": false, "icon": "MessagesSquare", "key": "ChatOutput", "legacy": false, "lf_version": "1.4.1", "metadata": {}, "minimized": true, "output_types": [], "outputs": [{"allows_loop": false, "cache": true, "display_name": "Message", "method": "message_response", "name": "message", "selected": "Message", "tool_mode": true, "types": ["Message"], "value": "__UNDEFINED__"}], "pinned": false, "score": 0.003169567463043492, "template": {"_type": "Component", "background_color": {"_input_type": "MessageTextInput", "advanced": true, "display_name": "Background Color", "dynamic": false, "info": "The background color of the icon.", "input_types": ["Message"], "list": false, "list_add_label": "Add More", "load_from_db": false, "name": "background_color", "placeholder": "", "required": false, "show": true, "title_case": false, "tool_mode": false, "trace_as_input": true, "trace_as_metadata": true, "type": "str", "value": ""}, "chat_icon": {"_input_type": "MessageTextInput", "advanced": true, "display_name": "Icon", "dynamic": false, "info": "The icon of the message.", "input_types": ["Message"], "list": false, "list_add_label": "Add More", "load_from_db": false, "name": "chat_icon", "placeholder": "", "required": false, "show": true, "title_case": false, "tool_mode": false, "trace_as_input": true, "trace_as_metadata": true, "type": "str", "value": ""}, "clean_data": {"_input_type": "BoolInput", "advanced": true, "display_name": "Basic Clean Data", "dynamic": false, "info": "Whether to clean the data", "list": false, "list_add_label": "Add More", "name": "clean_data", "placeholder": "", "required": false, "show": true, "title_case": false, "tool_mode": false, "trace_as_metadata": true, "type": "bool", "value": true}, "code": {"advanced": true, "dynamic": true, "fileTypes": [], "file_path": "", "info": "", "list": false, "load_from_db": false, "multiline": true, "name": "code", "password": false, "placeholder": "", "required": true, "show": true, "title_case": false, "type": "code", "value": "from collections.abc import Generator\nfrom typing import Any\n\nfrom langflow.base.io.chat import ChatComponent\nfrom langflow.inputs import BoolInput\nfrom langflow.inputs.inputs import HandleInput\nfrom langflow.io import DropdownInput, MessageTextInput, Output\nfrom langflow.schema.data import Data\nfrom langflow.schema.dataframe import Data<PERSON>rame\nfrom langflow.schema.message import Message\nfrom langflow.schema.properties import Source\nfrom langflow.utils.constants import (\n    MESSAGE_SENDER_AI,\n    MESSAGE_SENDER_NAME_AI,\n    MESSAGE_SENDER_USER,\n)\n\n\nclass ChatOutput(ChatComponent):\n    display_name = \"Chat Output\"\n    description = \"Display a chat message in the Playground.\"\n    icon = \"MessagesSquare\"\n    name = \"ChatOutput\"\n    minimized = True\n\n    inputs = [\n        HandleInput(\n            name=\"input_value\",\n            display_name=\"Text\",\n            info=\"Message to be passed as output.\",\n            input_types=[\"Data\", \"DataFrame\", \"Message\"],\n            required=True,\n        ),\n        BoolInput(\n            name=\"should_store_message\",\n            display_name=\"Store Messages\",\n            info=\"Store the message in the history.\",\n            value=True,\n            advanced=True,\n        ),\n        DropdownInput(\n            name=\"sender\",\n            display_name=\"Sender Type\",\n            options=[MESSAGE_SENDER_AI, MESSAGE_SENDER_USER],\n            value=MESSAGE_SENDER_AI,\n            advanced=True,\n            info=\"Type of sender.\",\n        ),\n        MessageTextInput(\n            name=\"sender_name\",\n            display_name=\"Sender Name\",\n            info=\"Name of the sender.\",\n            value=MESSAGE_SENDER_NAME_AI,\n            advanced=True,\n        ),\n        MessageTextInput(\n            name=\"session_id\",\n            display_name=\"Session ID\",\n            info=\"The session ID of the chat. If empty, the current session ID parameter will be used.\",\n            advanced=True,\n        ),\n        MessageTextInput(\n            name=\"data_template\",\n            display_name=\"Data Template\",\n            value=\"{text}\",\n            advanced=True,\n            info=\"Template to convert Data to Text. If left empty, it will be dynamically set to the Data's text key.\",\n        ),\n        MessageTextInput(\n            name=\"background_color\",\n            display_name=\"Background Color\",\n            info=\"The background color of the icon.\",\n            advanced=True,\n        ),\n        MessageTextInput(\n            name=\"chat_icon\",\n            display_name=\"Icon\",\n            info=\"The icon of the message.\",\n            advanced=True,\n        ),\n        MessageTextInput(\n            name=\"text_color\",\n            display_name=\"Text Color\",\n            info=\"The text color of the name\",\n            advanced=True,\n        ),\n        BoolInput(\n            name=\"clean_data\",\n            display_name=\"Basic Clean Data\",\n            value=True,\n            info=\"Whether to clean the data\",\n            advanced=True,\n        ),\n    ]\n    outputs = [\n        Output(\n            display_name=\"Message\",\n            name=\"message\",\n            method=\"message_response\",\n        ),\n    ]\n\n    def _build_source(self, id_: str | None, display_name: str | None, source: str | None) -> Source:\n        source_dict = {}\n        if id_:\n            source_dict[\"id\"] = id_\n        if display_name:\n            source_dict[\"display_name\"] = display_name\n        if source:\n            # Handle case where source is a ChatOpenAI object\n            if hasattr(source, \"model_name\"):\n                source_dict[\"source\"] = source.model_name\n            elif hasattr(source, \"model\"):\n                source_dict[\"source\"] = str(source.model)\n            else:\n                source_dict[\"source\"] = str(source)\n        return Source(**source_dict)\n\n    async def message_response(self) -> Message:\n        # First convert the input to string if needed\n        text = self.convert_to_string()\n        # Get source properties\n        source, icon, display_name, source_id = self.get_properties_from_source_component()\n        background_color = self.background_color\n        text_color = self.text_color\n        if self.chat_icon:\n            icon = self.chat_icon\n\n        # Create or use existing Message object\n        if isinstance(self.input_value, Message):\n            message = self.input_value\n            # Update message properties\n            message.text = text\n        else:\n            message = Message(text=text)\n\n        # Set message properties\n        message.sender = self.sender\n        message.sender_name = self.sender_name\n        message.session_id = self.session_id\n        message.flow_id = self.graph.flow_id if hasattr(self, \"graph\") else None\n        message.properties.source = self._build_source(source_id, display_name, source)\n        message.properties.icon = icon\n        message.properties.background_color = background_color\n        message.properties.text_color = text_color\n\n        # Store message if needed\n        if self.session_id and self.should_store_message:\n            stored_message = await self.send_message(message)\n            self.message.value = stored_message\n            message = stored_message\n\n        self.status = message\n        return message\n\n    def _validate_input(self) -> None:\n        \"\"\"Validate the input data and raise ValueError if invalid.\"\"\"\n        if self.input_value is None:\n            msg = \"Input data cannot be None\"\n            raise ValueError(msg)\n        if isinstance(self.input_value, list) and not all(\n            isinstance(item, Message | Data | DataFrame | str) for item in self.input_value\n        ):\n            invalid_types = [\n                type(item).__name__\n                for item in self.input_value\n                if not isinstance(item, Message | Data | DataFrame | str)\n            ]\n            msg = f\"Expected Data or DataFrame or Message or str, got {invalid_types}\"\n            raise TypeError(msg)\n        if not isinstance(\n            self.input_value,\n            Message | Data | DataFrame | str | list | Generator | type(None),\n        ):\n            type_name = type(self.input_value).__name__\n            msg = f\"Expected Data or DataFrame or Message or str, Generator or None, got {type_name}\"\n            raise TypeError(msg)\n\n    def _safe_convert(self, data: Any) -> str:\n        \"\"\"Safely convert input data to string.\"\"\"\n        try:\n            if isinstance(data, str):\n                return data\n            if isinstance(data, Message):\n                return data.get_text()\n            if isinstance(data, Data):\n                if data.get_text() is None:\n                    msg = \"Empty Data object\"\n                    raise ValueError(msg)\n                return data.get_text()\n            if isinstance(data, DataFrame):\n                if self.clean_data:\n                    # Remove empty rows\n                    data = data.dropna(how=\"all\")\n                    # Remove empty lines in each cell\n                    data = data.replace(r\"^\\s*$\", \"\", regex=True)\n                    # Replace multiple newlines with a single newline\n                    data = data.replace(r\"\\n+\", \"\\n\", regex=True)\n\n                # Replace pipe characters to avoid markdown table issues\n                processed_data = data.replace(r\"\\|\", r\"\\\\|\", regex=True)\n\n                processed_data = processed_data.map(\n                    lambda x: str(x).replace(\"\\n\", \"<br/>\") if isinstance(x, str) else x\n                )\n\n                return processed_data.to_markdown(index=False)\n            return str(data)\n        except (ValueError, TypeError, AttributeError) as e:\n            msg = f\"Error converting data: {e!s}\"\n            raise ValueError(msg) from e\n\n    def convert_to_string(self) -> str | Generator[Any, None, None]:\n        \"\"\"Convert input data to string with proper error handling.\"\"\"\n        self._validate_input()\n        if isinstance(self.input_value, list):\n            return \"\\n\".join([self._safe_convert(item) for item in self.input_value])\n        if isinstance(self.input_value, Generator):\n            return self.input_value\n        return self._safe_convert(self.input_value)\n"}, "data_template": {"_input_type": "MessageTextInput", "advanced": true, "display_name": "Data Template", "dynamic": false, "info": "Template to convert Data to Text. If left empty, it will be dynamically set to the Data's text key.", "input_types": ["Message"], "list": false, "list_add_label": "Add More", "load_from_db": false, "name": "data_template", "placeholder": "", "required": false, "show": true, "title_case": false, "tool_mode": false, "trace_as_input": true, "trace_as_metadata": true, "type": "str", "value": "{text}"}, "input_value": {"_input_type": "HandleInput", "advanced": false, "display_name": "Text", "dynamic": false, "info": "Message to be passed as output.", "input_types": ["Data", "DataFrame", "Message"], "list": false, "list_add_label": "Add More", "name": "input_value", "placeholder": "", "required": true, "show": true, "title_case": false, "trace_as_metadata": true, "type": "other", "value": ""}, "sender": {"_input_type": "DropdownInput", "advanced": true, "combobox": false, "dialog_inputs": {}, "display_name": "Sender Type", "dynamic": false, "info": "Type of sender.", "name": "sender", "options": ["Machine", "User"], "options_metadata": [], "placeholder": "", "required": false, "show": true, "title_case": false, "toggle": false, "tool_mode": false, "trace_as_metadata": true, "type": "str", "value": "Machine"}, "sender_name": {"_input_type": "MessageTextInput", "advanced": true, "display_name": "Sender Name", "dynamic": false, "info": "Name of the sender.", "input_types": ["Message"], "list": false, "list_add_label": "Add More", "load_from_db": false, "name": "sender_name", "placeholder": "", "required": false, "show": true, "title_case": false, "tool_mode": false, "trace_as_input": true, "trace_as_metadata": true, "type": "str", "value": "AI"}, "session_id": {"_input_type": "MessageTextInput", "advanced": true, "display_name": "Session ID", "dynamic": false, "info": "The session ID of the chat. If empty, the current session ID parameter will be used.", "input_types": ["Message"], "list": false, "list_add_label": "Add More", "load_from_db": false, "name": "session_id", "placeholder": "", "required": false, "show": true, "title_case": false, "tool_mode": false, "trace_as_input": true, "trace_as_metadata": true, "type": "str", "value": ""}, "should_store_message": {"_input_type": "BoolInput", "advanced": true, "display_name": "Store Messages", "dynamic": false, "info": "Store the message in the history.", "list": false, "list_add_label": "Add More", "name": "should_store_message", "placeholder": "", "required": false, "show": true, "title_case": false, "tool_mode": false, "trace_as_metadata": true, "type": "bool", "value": true}, "text_color": {"_input_type": "MessageTextInput", "advanced": true, "display_name": "Text Color", "dynamic": false, "info": "The text color of the name", "input_types": ["Message"], "list": false, "list_add_label": "Add More", "load_from_db": false, "name": "text_color", "placeholder": "", "required": false, "show": true, "title_case": false, "tool_mode": false, "trace_as_input": true, "trace_as_metadata": true, "type": "str", "value": ""}}, "tool_mode": false}, "showNode": false, "type": "ChatOutput"}, "dragging": false, "id": "ChatOutput-9jsnO", "measured": {"height": 66, "width": 192}, "position": {"x": 2471.3143292418977, "y": 832.7888356096034}, "selected": false, "type": "genericNode"}], "viewport": {"x": -43.419583305059405, "y": -25.79876409873117, "zoom": 0.581082931088503}}, "description": "Perform basic prompting with an OpenAI model.", "endpoint_name": null, "id": "f3c3b56c-f5f6-44aa-9c13-67447e55a652", "is_component": false, "last_tested_version": "1.4.1", "name": "Cleanlab Evaluator and Remediator", "tags": ["chatbots"]}