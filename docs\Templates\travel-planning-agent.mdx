---
title: Travel planning agent
slug: /travel-planning-agent
---

import Icon from "@site/src/components/icon";

This flow demonstrates using multiple [Agent](/agents) components in a single flow.

In this flow, multiple **Agents** plan a travel journey.
Each agent is given a different responsibility defined by its **System Prompt** field.
The **Chat input** defines where the user wants to go, and passes the result to the **City Selection** agent. The **Local Expert** agent then adds information based on the selected cities, and the **Travel Concierge** assembles a seven day travel plan in Markdown.

All agents have access to the **Search API** and **URL Content Fetcher** components, while only the **Travel Concierge** agent can use the **Calculator** for computing the trip costs.

## Prerequisites

- [A running Langflow instance](/get-started-installation)
- [An OpenAI API key](https://platform.openai.com/)
- [A Search API key](https://www.searchapi.io/)

## Open Langflow and start a new flow

Click **New Flow**, and then select the **Travel Planning Agent** flow.

This opens a starter flow with the necessary components to run an agentic application using multiple Tool-calling agents.

## Create the travel planning agent flow

![](/img/starter-flow-travel-agent.png)

The **Travel Planning Agent** flow consists of these components:

* Multiple **Tool calling agent** components that use the connected LLM to reason through the user's input and select among the connected tools to complete their tasks.
* The **Calculator** component performs basic arithmetic operations.
* The **URL Content Fetcher** component scrapes content from a given URL.
* The **Chat Input** component accepts user input to the chat.
* The **Chat Output** component prints the flow's output to the chat.
* The **OpenAI** model component sends the user input and prompt to the OpenAI API and receives a response.

## Run the travel planning agent flow

1. Add your credentials to the OpenAI and Search API components.
2. To run the flow, click <Icon name="Play" aria-hidden="true"/> **Playground**.
You should receive a detailed, helpful answer to the journey defined in the **Chat input** component.

Now that your query has completed the journey from **Chat input** to **Chat output**, you have completed the **Travel Planning Agent** flow.
