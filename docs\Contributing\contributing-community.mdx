---
title: Join the Langflow community
slug: /contributing-community
---

There are several ways you can interact with the Langflow community and learn more about the Langflow codebase.

## Join the Langflow Discord server

Join the [Langflow Discord Server](https://discord.gg/EqksyE2EX9) to ask questions and showcase your projects.

## Follow Langflow on X

Follow [@langflow_ai](https://twitter.com/langflow_ai) on X to get the latest news about Langflow.

## Star Langflow on GitHub

If you like Lang<PERSON>, you can star the [Langflow GitHub repository](https://github.com/langflow-ai/langflow).
Stars help other users find Lang<PERSON> more easily, and quickly understand that other users have found it useful.

Because Langflow is an open-source project, the more visible the repository is, the more likely the project is to attract [contributors](/contributing-how-to-contribute).

## Watch the GitHub repository

You can watch the [Langflow GitHub repository](https://github.com/langflow-ai/langflow) to get notified about new releases and other repository activity.

To get release notifications only, select **Releases only**.

If you select **Watching**, you will receive notifications about new releases as well as issues, discussions, and pull requests, if you are interested in that activity.
For information about customizing repository notifications, see the [GitHub documentation on repository subscriptions](https://docs.github.com/en/account-and-profile/managing-subscriptions-and-notifications-on-github/managing-subscriptions-for-activity-on-github/viewing-your-subscriptions).

## Request enhancements and get help through GitHub

You can also submit feature requests and get help with Langflow through the GitHub repository.
For more information, see [Get help and request enhancements](/contributing-github-issues).