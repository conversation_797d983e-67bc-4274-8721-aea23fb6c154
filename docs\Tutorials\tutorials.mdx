---
title: Tutorials
slug: /tutorials
---

# Tutorials

Learn how to build AI applications with Axie Studio through these step-by-step tutorials.

## Getting Started Tutorials

### [Chat with RAG](/chat-with-rag)
Build a Retrieval-Augmented Generation (RAG) system that can answer questions based on your documents.

### [Chat with Files](/chat-with-files)
Create a chatbot that can process and answer questions about uploaded files.

### [Agent Tutorial](/agent)
Learn how to build intelligent agents that can use tools and make decisions.

### [Model Context Protocol (MCP) Tutorial](/mcp-tutorial)
Integrate MCP servers to extend your AI applications with external data sources and tools.

## Next Steps

After completing these tutorials, explore the [Flow Templates](/basic-prompting) for more advanced use cases, or dive into the [Components reference](/concepts-components) to understand all available building blocks.
