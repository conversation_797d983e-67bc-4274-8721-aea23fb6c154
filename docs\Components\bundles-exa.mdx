---
title: Exa
slug: /bundles-exa
---

import Icon from "@site/src/components/icon";

[Bundles](/components-bundle-components) contain custom components that support specific third-party integrations with Langflow.

This page describes the components that are available in the **Exa** bundle.

## Exa Search

This component provides an [Exa Search](https://exa.ai/) toolkit for search and content retrieval by a [Langflow agent](/agents) or [MCP client](/mcp-client).

The output is exclusively [`Tools`](/data-types#tool).

### Exa Search parameters

| Name | Type | Description |
|------|------|-------------|
| Exa Search API Key (`metaphor_api_key`) | SecretString | Input parameter. An API key for Exa Search. |
| Use Autoprompt (`use_autoprompt`) | Boolean | Input parameter. Whether to use the autoprompt feature. Default: true. |
| Search Number of Results (`search_num_results`) | Integer | Input parameter. The number of results to return for search. Default: 5. |
| Similar Number of Results (`similar_num_results`) | Integer | Input parameter. The number of similar results to return. Default: 5. |