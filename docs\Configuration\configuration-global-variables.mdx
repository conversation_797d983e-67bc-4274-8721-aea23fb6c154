---
title: Global variables
slug: /configuration-global-variables
---

import Icon from "@site/src/components/icon";
import Tabs from '@theme/Tabs';
import TabItem from '@theme/TabItem';

Global variables let you store and reuse generic input values and credentials across your projects.
You can use a global variable in any text input field that displays the <Icon name="Globe" aria-hidden="true"/> **Globe** icon.

Langflow stores global variables in its internal database, and encrypts the values using a secret key.

## Create a global variable

To create a new global variable, follow these steps.

1. In the Langflow UI header, click your profile icon, and then select **Settings**.
2. Click **Global Variables**.

3. Click **Add New**.

4. In the **Create Variable** dialog, enter a name for your variable in the **Variable Name** field.

5. Optional: Select a **Type** for your global variable. The available types are **Generic** (default) and **Credential**.

   Langflow encrypts both **Generic** and **Credential** type global variables. The difference is in how the variables are displayed in the UI.

   Global variables of the **Generic** type are displayed in a standard input field with no masking.

   Global variables of the **Credential** type are hidden in the UI and entered in a password-style input field that masks the value. They are also not allowed in session ID fields, where they would be exposed.
   All default environment variables and variables sourced from the environment using `LANGFLOW_VARIABLES_TO_GET_FROM_ENVIRONMENT` are automatically set as **Credential** type global variables.

6. Enter the **Value** for your global variable.

7. Optional: Use the **Apply To Fields** menu to select one or more fields that you want Langflow to automatically apply your global variable to. For example, if you select **OpenAI API Key**, Langflow automatically applies the variable to any **OpenAI API Key** field.

8. Click **Save Variable**.

You can now select your global variable from any text input field that displays the <Icon name="Globe" aria-hidden="true"/> **Globe** icon.

## Edit a global variable

1. In the Langflow UI header, click your profile icon, and then select **Settings**.

2. Click **Global Variables**.

3. Click on the global variable you want to edit.

4. In the **Update Variable** dialog, you can edit the following fields: **Variable Name**, **Value**, and **Apply To Fields**.

5. Click **Update Variable**.

## Delete a global variable

:::warning
Deleting a global variable permanently deletes any references to it from your existing projects.
:::

1. In the Langflow UI header, click your profile icon, and then select **Settings**.

2. Click **Global Variables**.

3. Click the checkbox next to the global variable that you want to delete.

4. Click <Icon name="Trash2" aria-hidden="true"/> **Delete**.

The global variable, and any existing references to it, are deleted.

## Add custom global variables from the environment

You can use the `LANGFLOW_VARIABLES_TO_GET_FROM_ENVIRONMENT` environment variable to source custom global variables from your runtime environment.
All global variables sourced from the environment are automatically set as **Credential** type global variables.

Langflow's [default global variables](#default-environment-variables) are already included in this list and are automatically sourced when detected.
You can extend this list by setting `LANGFLOW_VARIABLES_TO_GET_FROM_ENVIRONMENT` with your additional variables.

<Tabs>

<TabItem value="local" label="Local" default>

If you installed Langflow locally, you must define the `LANGFLOW_VARIABLES_TO_GET_FROM_ENVIRONMENT` environment variable in a `.env` file.

1. Create a `.env` file and open it in your preferred editor.

2. Add the `LANGFLOW_VARIABLES_TO_GET_FROM_ENVIRONMENT` environment variable as follows:

   You can specify the variables either as a comma-separated string with no spaces, or as a JSON list:

   ```text
   # Option 1: Comma-separated string (no spaces)
   LANGFLOW_VARIABLES_TO_GET_FROM_ENVIRONMENT=VARIABLE1,VARIABLE2

   # Option 2: JSON list format
   LANGFLOW_VARIABLES_TO_GET_FROM_ENVIRONMENT=["VARIABLE1", "VARIABLE2"]
   ```

   Replace `VARIABLE1,VARIABLE2` with your additional variables that you want Langflow to source from the environment, such as `my_key,some_string` or `["my_key", "some_string"]`.
   These are added to the default list of variables that Langflow already monitors.

3. Save and close the file.

4. Start Langflow with the `.env` file:

   ```bash
   VARIABLE1="VALUE1" VARIABLE2="VALUE2" python -m langflow run --env-file .env
   ```

   :::note
   In this example, the environment variables (`VARIABLE1="VALUE1"` and `VARIABLE2="VALUE2"`) are prefixed to the startup command.
   This is a rudimentary method for exposing environment variables to Python on the command line, and is meant for illustrative purposes.
   Make sure to expose your environment variables to Langflow in a manner that best suits your own environment.
   :::

5. Confirm that Langflow successfully sourced the global variables from the environment:

   1. In the Langflow UI header, click your profile icon, and then select **Settings**.

   2. Click **Global Variables**, and then make sure that your environment variables appear in the **Global Variables** list.

</TabItem>

<TabItem value="docker" label="Docker">

If you're using Docker, you can pass `LANGFLOW_VARIABLES_TO_GET_FROM_ENVIRONMENT` directly from the command line or from a `.env` file.

To pass `LANGFLOW_VARIABLES_TO_GET_FROM_ENVIRONMENT` directly from the command line:

```bash
docker run -it --rm \
    -p 7860:7860 \
    -e LANGFLOW_VARIABLES_TO_GET_FROM_ENVIRONMENT="VARIABLE1,VARIABLE2" \
    -e VARIABLE1="VALUE1" \
    -e VARIABLE2="VALUE2" \
    langflowai/langflow:latest
```

To pass `LANGFLOW_VARIABLES_TO_GET_FROM_ENVIRONMENT` from a `.env` file:

```bash
docker run -it --rm \
    -p 7860:7860 \
    --env-file .env \
    -e VARIABLE1="VALUE1" \
    -e VARIABLE2="VALUE2" \
    langflowai/langflow:latest
```

</TabItem>

</Tabs>

:::info
When adding global variables from the environment, the following limitations apply:

- You can only source the **Name** and **Value** from the environment.
  To add additional parameters, such as the **Apply To Fields** parameter, you must edit the global variables in the Langflow UI.

- Global variables that you add from the environment always have the **Credential** type.
:::


If you want to explicitly prevent Langflow from sourcing global variables from the environment, set `LANGFLOW_STORE_ENVIRONMENT_VARIABLES` to `false` in your `.env` file:

```text
LANGFLOW_STORE_ENVIRONMENT_VARIABLES=false
```

If you want to automatically set fallback values for your global variables from environment variables, set the `LANGFLOW_FALLBACK_FROM_ENV_VAR` environment variable to `true` in your `.env` file. When this feature is enabled, if a global variable is not found, Langflow attempts to use an environment variable with the same name as a fallback.

```text
LANGFLOW_FALLBACK_FROM_ENV_VAR=true
```

## Default environment variables

Langflow automatically detects and converts some environment variables into global variables of the type **Credential**, which are applied to the specific fields in components that require them. Currently, the following variables are supported:

- `ANTHROPIC_API_KEY`
- `ASTRA_DB_API_ENDPOINT`
- `ASTRA_DB_APPLICATION_TOKEN`
- `AWS_ACCESS_KEY_ID`
- `AWS_SECRET_ACCESS_KEY`
- `AZURE_OPENAI_API_DEPLOYMENT_NAME`
- `AZURE_OPENAI_API_EMBEDDINGS_DEPLOYMENT_NAME`
- `AZURE_OPENAI_API_INSTANCE_NAME`
- `AZURE_OPENAI_API_KEY`
- `AZURE_OPENAI_API_VERSION`
- `COHERE_API_KEY`
- `COMPOSIO_API_KEY`
- `GOOGLE_API_KEY`
- `GROQ_API_KEY`
- `HUGGINGFACEHUB_API_TOKEN`
- `NOVITA_API_KEY`
- `OPENAI_API_KEY`
- `PINECONE_API_KEY`
- `SAMBANOVA_API_KEY`
- `SEARCHAPI_API_KEY`
- `SERPAPI_API_KEY`
- `TAVILY_API_KEY`
- `UPSTASH_VECTOR_REST_TOKEN`
- `UPSTASH_VECTOR_REST_URL`
- `VECTARA_API_KEY`
- `VECTARA_CORPUS_ID`
- `VECTARA_CUSTOMER_ID`


For information about other environment variables and their usage, see [Environment Variables](/environment-variables).
