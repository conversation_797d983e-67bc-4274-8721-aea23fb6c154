---
title: Baidu
slug: /bundles-baidu
---

import Icon from "@site/src/components/icon";

[Bundles](/components-bundle-components) contain custom components that support specific third-party integrations with Langflow.

This page describes the components that are available in the **Baidu** bundle.

## Qianfan

The **Qianfan** component generates text using Qianfan's language models.

It can output either a **Model Response** ([`Message`](/data-types#message)) or a **Language Model** ([`LanguageModel`](/data-types#languagemodel)).

Use the **Language Model** output when you want to use a Qianfan model as the LLM for another LLM-driven component, such as a **Language Model** or **Smart Function** component.

For more information, see [**Language Model** components](/components-models) and [Qianfan documentation](https://github.com/baidubce/bce-qianfan-sdk).