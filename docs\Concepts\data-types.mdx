---
title: Langflow data types
slug: /data-types
---

import Tabs from '@theme/Tabs';
import TabItem from '@theme/TabItem';
import Icon from "@site/src/components/icon";

Langflow components are designed to accept and produce specific types of inputs and outputs.
Input and output data types define the structure and flow of information between components.
Understanding these structures helps you build applications that provide valid input and correctly anticipate the output format.

[Component ports](/concepts-components#component-ports) represent the data types that each component can send and receive.
Some data types are self-evident from the fields they are attached to; for example, a **System Message** field accepts [message data](#message).
[Port colors](/concepts-components#port-colors) also indicate the port's data type.
For example **Data** ports, represented by <Icon name="Circle" size="16" aria-label="Red data port" style={{ color: '#dc2626', fill: '#dc2626' }} />, either accept or emit [structured data objects](#data).

When building flows, connect output ports to input ports of the same type (color) to transfer that type of data between two components.

:::tip
* In the visual editor, hover over a port to see connection details for that port.

* Click a port to filter the **Components** menu by compatible components.

* If two components have incompatible data types, you can use a processing component like the [**Type Convert** component](/components-processing#type-convert) to convert the data between components.
:::

## Data

**Data** ports <Icon name="Circle" size="16" aria-label="Red data port" style={{ color: '#dc2626', fill: '#dc2626' }} /> accept or produce the `Data` type, which is a structured data object, like a JSON payload that you might send to an API.
This data type is used to pass key-value pairs between components, such as user profiles, settings, or other structured information.

`Data` objects include a primary text field, indicated by a `text_key`, and additional metadata.

### Schema and attributes

The schema is defined in [`data.py`](https://github.com/langflow-ai/langflow/blob/main/src/backend/base/langflow/schema/data.py).

The following attributes are available:

- `data`: A dictionary that stores key-value pairs.
- `text_key`: The key in `data` that is considered the primary text value.
- `default_value`: Fallback if `text_key` is missing. The default `text_key` is `"text"`.

### Data structure

A `Data` object stores key-value pairs within the `.data` attribute, where each key is a field name and its value can be any supported data type. `text_key` tells Langflow which key in the data dictionary is the primary text value for that object.

```python
data_obj = Data(
    text_key="text",            # Field 1
    data={                      # Field 2 (the actual dict)
        "text": "Hello world",
        "name": "Charlie",
        "age": 28
    },
    default_value=""            # Field 3
)
```

`Data` objects can be serialized to JSON, created from JSON, or created from other dictionary data.
However, the resulting `Data` object is a structured object with validation and methods, not a plain dictionary.

For example, when serialized into JSON, the previous example becomes the following JSON object:

```json
{
  "text_key": "text",
  "data": {
    "text": "User Profile",
    "name": "Charlie Lastname",
    "age": 28,
    "email": "<EMAIL>"
  },
  "default_value": ""
}
```

## DataFrame

**DataFrame** ports <Icon name="Circle" size="16" aria-label="Pink dataframe port" style={{ color: '#ec4899', fill:'#ec4899' }} /> accept or produce [pandas DataFrames](https://pandas.pydata.org/docs/reference/api/pandas.DataFrame.html), which are similar to the tabular CSV data.

Use the `DataFrame` type to work with data containing multiple rows or records.

### Schema and attributes

The schema is defined in [`dataframe.py`](https://github.com/langflow-ai/langflow/blob/main/src/backend/base/langflow/schema/dataframe.py).

The following attributes are available:

- **Full pandas compatibility**: All pandas DataFrame methods and functionality are supported

- **Langflow integration**: Accepts lists of [`Data` objects](#data), dictionaries, or existing DataFrames.

- **Convenience methods**:
   - `to_data_list()`
   - `add_row()`
   - `add_rows()`
   - `to_lc_documents()`
   - `to_data()`
   - `to_message()`

- **Text key support**: Maintains `text_key` and `default_value` attributes for [`Data` object](#data) compatibility.

### DataFrame structure

A DataFrame has a tabular data structure with rows and columns.
Keys are columns, and each object in the array is a row.

```json
[
  {
    "name": "Charlie Lastname",
    "age": 28,
    "email": "<EMAIL>"
  },
  {
    "name": "Alexandra Example",
    "age": 34,
    "email": "<EMAIL>"
  }
]
```

When represented as tabular data, the preceding DataFrame object is structured as follows:

```text
| name | age | email |
|------|-----|-------|
| Charlie Lastname | 28 | <EMAIL> |
| Alexandra Example | 34 | <EMAIL> |
```

## Embeddings

**Embeddings** ports <Icon name="Circle" size="16" aria-label="Emerald embeddings port" style={{ color: '#10b981', fill: '#10b981' }} /> emit or ingest vector embeddings to support functions like similarity search.

The `Embeddings` data type is used specifically by components that either produce or consume vector embeddings, such as the [embedding model components](/components-embedding-models) and [vector store components](/components-vector-stores).

For example, the **Embedding Model** component outputs `Embeddings` data that you can connect to an **Embedding** input port on a vector store component.

For information about the underlying Python classes that produce `Embeddings`, see the [LangChain Embedding models documentation](https://python.langchain.com/docs/integrations/text_embedding/).

## LanguageModel

The `LanguageModel` type is a specific data type that can be produced by language model components and accepted by components that use an LLM.

When you change a language model component's output type from **Model Response** to **Language Model**, the component's output port changes from a **Message** port to a **Language Model** port <Icon name="Circle" size="16" aria-label="Fuchsia language model port" style={{ color: '#c026d3', fill: '#c026d3' }} />.

Then, you connect the outgoing **Language Model** port to a **Language Model** input port on a compatible component, such as a **Smart Function** component.

For more information about using language model components in flows and toggling `LanguageModel` output, see [**Language Model** components](/components-models#language-model-output-types).

<details>
<summary>LanguageModel is an instance of LangChain ChatModel</summary>

Because Langflow is built on LangChain, `LanguageModel` is actually an instance of a [LangChain chat model](https://python.langchain.com/docs/concepts/chat_models/) that uses the configuration parameters set in the originating component.

Often, components produce an instance of an [integrated chat model](https://python.langchain.com/docs/integrations/chat/) that is designed to support the specific model provider, such as [`ChatOpenAI`](https://python.langchain.com/docs/integrations/chat/openai/) or [`ChatAnthropic`](https://python.langchain.com/docs/integrations/chat/anthropic/).

You can inspect the [component code](/concepts-components#component-code) to see the specific `Chat` instance it produces.

</details>

## Memory

**Memory** ports <Icon name="Circle" size="16" aria-label="Orange memory port" style={{ color: '#f97316', fill: '#f97316' }} /> are used to integrate a **Message History** component with external chat memory storage.

For more information, see the [**Message History** component](/components-helpers#message-history).

## Message

**Message** ports <Icon name="Circle" size="16" aria-label="Indigo message port" style={{ color: '#4f46e5', fill: '#4f46e5' }} /> accept or produce `Message` data, which extends the [`Data` type](#data) with additional fields and methods for text input typically used in chat flows.

This data type is used by many components.

:::tip
Components that accept or produce `Message` data may not include all attributes in the incoming or outgoing `Message` data.
As long as the data is compatible with the `Message` schema, it can be valid.

When building flows, focus on the fields shown on each component in the visual editor, rather than the data types passed between components.
The details of a particular data type are often only relevant when you are debugging a flow or component that isn't producing the expected output.

For example, a **Chat Input** component only requires the content of the **Input Text** (`input_value`) field.
The component then constructs a complete `Message` object before passing the data to other components in the flow.
:::

### Schema, structure, and attributes

The `Message` schema is defined in [`message.py`](https://github.com/langflow-ai/langflow/blob/main/src/backend/base/langflow/schema/message.py).
Some `Message` attributes have their own schema definitions, such as [`content_block.py`](https://github.com/langflow-ai/langflow/blob/main/src/backend/base/langflow/schema/content_block.py).

`Message` data is structured as a JSON object.
For example:

```json
{
  "text": "Name: Charlie Lastname, Age: 28, Email: <EMAIL>",
  "sender": "User",
  "sender_name": "Charlie Lastname",
  "session_id": "some-session-id",
  "timestamp": "2024-06-01T12:00:00Z",
  "files": [],
  "content_blocks": [],
  "category": "message"
}
```

The attributes included in a specific `Message` object depend on the context, including the component type, flow activity, and whether the message is a query or response.
Some common attributes include the following:

- `text`: The main message content.
- `sender`: Identifies the originator of a chat message as either `User` or `Language Model`.
- `sender_name`: The display name for the sender. Defaults to `User` or `Language Model`.
- `session_id`: The chat [session identifier](/session-id).
- `flow_id`: The ID of the flow that the message is associated with. `flow_id` and `session_id` are the same if the flow doesn't use custom session IDs.
- `timestamp`: The UTC timestamp that the message was sent.
- `files`: A list of file paths or images included with the message
- `content_blocks`: Container for rich content input, such as text, media, or code. Also contains error message information if the LLM can't process the input.
- `category`: `"message"`, `"error"`, `"warning"`, or `"info"`.

Not all attributes are required, and some components accept message-compatible input, such as raw text input.
The strictness depends on the component.

### Message data in Input/Output components

In flows with [**Chat Input/Output** components](/components-io#chat-io), `Message` data provides a consistent structure for chat interactions, and it is ideal for chatbots, conversational analysis, and other use cases based on a dialog with an LLM or agent.
In these flows, the **Playground** chat interface prints only the `Message` attributes that are relevant to the conversation, such as `text`, `files`, and error messages from `content_blocks`.
To see all `Message` attributes, inspect the message logs in the **Playground**.

In flows with [**Text Input/Output** components](/components-io#text-io), `Message` data is used to pass simple text strings without the chat-related metadata.
These components handle `Message` data as independent text strings, not as part of an ongoing conversation.
For this reason, a flow with only **Text Input/Output** components isn't compatible with the **Playground**.
For more information, see [Input/Output components](/components-io).

When using the Langflow API, the response includes the `Message` object along with other response data from the flow run.
Langflow API responses can be extremely verbose, so your applications must include code to extract relevant data from the response to return to the user.
For an example, see the [Langflow quickstart](/get-started-quickstart).

Additionally, input sent to the input port of input/output components does _not_ need to be a complete `Message` object because the component constructs the `Message` object that is then passed to other components in the flow or returned as flow output.
In fact, some components should not receive a complete `Message` object because some attributes, like `timestamp` should be added by the component for accuracy.

## Tool

**Tool** ports <Icon name="Circle" size="16" aria-label="Cyan tool port" style={{ color: '#06b6d4', fill: '#06b6d4' }} /> connect tools to an **Agent** component.

Tools can be other components where you enabled **Tool Mode**, they can be the dedicated **MCP Tools** component, or they can be other components that only support **Tool Mode**.
Multiple tools can be connected to the same **Agent** component at the same port.

Functionally, `Tool` data is a LangChain `StructuredTool` object that can be used in agent workflows.

For more information, see [Configure tools for agents](/agents-tools) and [Use Langflow as an MCP client](/mcp-client).

## Unknown or multiple types

If a port can accept or produce multiple data types, it is represented by the gray port icon <Icon name="Circle" size="16" aria-label="Gray unknown port" style={{ color: '#9CA3AF', fill: '#9CA3AF' }} />.

Hover over the port to see the accepted or produced data types.

## View data types in flows

In Langflow, you can use <Icon name="TextSearch" aria-hidden="True" /> **Inspect output** to view the output of individual components.
This can help you learn about the different data type and debug problems with invalid or malformed inputs and output.

The following example shows how to inspect the output of a **Type Convert** component, which can convert `Message`, `Data`, or `DataFrame` input into `Message`, `Data`, or `DataFrame` output:

1. Create a flow, and then connect a **Chat Input** component to a **Type Convert** component.

2. In the **Chat Input** component, enter some text for the type converter to process.

3. On the **Type Convert** component, click <Icon name="Play" aria-hidden="true"/> **Run component**, and then click <Icon name="TextSearch" aria-hidden="True" /> **Inspect output**.

    The default output is `Message` data, which is the same as the input coming from the **Chat Input** component.
    To see the `Message` data converted to `Data` or `DataFrame`, change the **Output Type** on the **Type Convert** component, and then rerun the component.

    <Tabs groupId="Data type">
      <TabItem value="Message" label="Message" default>

    ```text
    Input text
    ```

      </TabItem>
      <TabItem value="Data" label="Data">

    ```json
    {
      "timestamp": "2025-07-15 20:56:20 UTC",
      "sender": "User",
      "sender_name": "User",
      "session_id": "a0c7e888-4fd6-4242-b8c8-e761ad690aeb",
      "text": "",
      "files": [],
      "error": false,
      "edit": false,
      "properties": {
        "text_color": "",
        "background_color": "",
        "edited": false,
        "source": {
          "id": null,
          "display_name": null,
          "source": null
        },
        "icon": "",
        "allow_markdown": false,
        "positive_feedback": null,
        "state": "complete",
        "targets": []
      },
      "category": "message",
      "content_blocks": [],
      "id": "9da72da2-efbb-4ccd-90ad-b32429b0418e",
      "flow_id": "a0c7e888-4fd6-4242-b8c8-e761ad690aeb",
      "duration": null
    }
    ```

      </TabItem>
      <TabItem value="DataFrame" label="DataFrame">

    ```text
    | Field | Value |
    |-------|-------|
    | timestamp | 2025-07-15 20:56:11 UTC |
    | sender | User |
    | sender_name | User |
    | session_id | a0c7e888-4fd6-4242-b8c8-e761ad690aeb |
    | text | (empty) |
    | files | [] |
    | error | False |
    | edit | False |
    | properties | text_color: '', background_color: '', edited: False, source: {id: None, display_name: None, source: None}, icon: '', allow_markdown: False, positive_feedback: None, state: 'complete', targets: [] |
    | category | message |
    | content_blocks | [] |
    | id | 341686eb-7a39-4b80-a90a-d8bf267815ef |
    | flow_id | a0c7e888-4fd6-4242-b8c8-e761ad690aeb |
    | duration | (empty) |
    ```

      </TabItem>
    </Tabs>

## See also

- [Custom components](/components-custom-components)
- [Pydantic Models](https://docs.pydantic.dev/latest/api/base_model/)
- [pandas.DataFrame](https://pandas.pydata.org/docs/reference/api/pandas.DataFrame.html)