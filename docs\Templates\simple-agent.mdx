---
title: Simple agent
slug: /simple-agent
---

import Icon from "@site/src/components/icon";

This flow demonstrates using an [Agent](/agents) in a flow.
An **Agent** uses an LLM as its "brain" to select among the connected tools and complete its tasks.

In this flow, the **Agent** reasons using a connected [Language model](/components-models) component.
The **Agent** selects the [Calculator](/components-helpers#calculator) tool for simple math problems, and the [URL](/components-data#url) tool to search a URL for content.

## Prerequisites

- [A running Langflow instance](/get-started-installation)
- [An OpenAI API key](https://platform.openai.com/)

## Open Langflow and start a new flow

1. From the Langflow dashboard, click **New Flow**.

2. Select **Simple Agent**.

The **Simple Agent** flow is created.

![Simple agent starter flow](/img/quickstart-simple-agent-flow.png)

## Run the simple agent flow

1. Add your **OpenAI API key** to the **Agent** component.

	Optionally, create a [global variable](/configuration-global-variables) for the **OpenAI API key**.

    1. In the **OpenAI API Key** field, click <Icon name="Globe" aria-hidden="True" /> **Globe**, and then click **Add New Variable**.
    2. In the **Variable Name** field, enter `openai_api_key`.
    3. In the **Value** field, paste your OpenAI API Key (`sk-...`).
    4. Click **Save Variable**.

2. To run the flow, click <Icon name="Play" aria-hidden="true"/> **Playground**.
3. To confirm the tools are connected, ask the agent, `What tools are available to you?`
    The response is similar to the following:
    ```text
    I have access to the following tools:
    Calculator: Perform basic arithmetic operations.
    fetch_content: Load and retrieve data from specified URLs.
    fetch_content_text: Load and retrieve text from specified URLs.
    as_dataframe: Load and retrieve data in a structured format (dataframe) from specified URLs.
    get_current_date: Returns the current date and time in a selected timezone.
    ```
4. Ask the agent a question. For example, ask it to create a tabletop character using your favorite rules set.
    The agent tells you when it's using the `URL-fetch_content_text` tool to search for rules information, and when it's using `CalculatorComponent-evaluate_expression` to generate attributes with dice rolls.
    The final output should be similar to this:

    ```text
    Final Attributes
    Strength (STR): 10
    Constitution (CON): 12
    Size (SIZ): 14
    Dexterity (DEX): 9
    Intelligence (INT): 11
    Power (POW): 13
    Charisma (CHA): 8
    ```

Now that your query has completed the journey from **Chat input** to **Chat output**, you have completed the **Simple Agent** flow.
